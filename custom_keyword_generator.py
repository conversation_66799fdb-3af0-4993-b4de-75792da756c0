import pandas as pd
import re
from datetime import datetime

# ==============================================================================
# ### STEP 1: 个性化修改区域 - 在这里定义你要推广的软件 ###
# ==============================================================================
# 你只需要修改下面的 SOFTWARE_CONFIG 字典，就能为任何新软件生成关键词。

SOFTWARE_CONFIG = {
    # 软件主名：将用于生成文件名，如 "微信_keywords.csv"
    "main_name": "微信",
    
    # 软件别名/核心词：尽可能多地列出用户可能搜索的名称
    "aliases": [
        "微信", "WeChat", "微信电脑版", "微信PC版", "微信桌面版", "电脑版微信",
        "PC版微信", "桌面版微信", "微信客户端", "PC微信", "电脑微信"
    ],
    
    # 软件核心功能/场景：这是生成"超级长尾词"的关键，列出用户会关心的具体功能
    "features": []
}


# ==============================================================================
# ### STEP 2: (可选) 调整关键词“后缀” - 按需增删 ###
# ==============================================================================
# 下面的列表是关键词的“弹药库”，你可以根据行业特点自由增删。

# --- 核心下载意图 ---
DOWNLOAD_INTENT = [
    "下载", "免费下载", "安全下载", "立即下载", "高速下载", "本地下载",
    "在线安装", "安装包", "安装", "离线安装包", "完整版", "安装程序",
    "下载器", "下载地址", "下载链接", "一键安装"
]

# --- 版本与更新意图 ---
# (年份会自动动态生成)
VERSION_INTENT = [
    "最新版", "新版", "最新版本", "正式版", "稳定版", "旧版本", "历史版本",
    "经典版", "怀旧版", "所有版本", "各版本"
]

# --- PC技术规格意图 ---
TECH_SPEC_INTENT = [
    "64位", "32位", "x64", "x86", "Windows版", "Windows", "Win", 
    "Windows 11", "Windows 10", "Win11", "Win10", "Win7", "for Windows",
    "电脑专用", "PC专用"
]

# --- “风险”与价值意图 (用于拦截转化) ---
RISK_VALUE_INTENT = [
    "绿色版", "免安装版", "纯净版", "去广告版", "精简版", "便携版", "直装版"
]

# --- 问题导向意图 (用于场景破圈) ---
PROBLEM_INTENT = [
    "怎么用", "如何安装"
]


# ==============================================================================
# ### 核心生成逻辑 - 一般无需修改此部分 ###
# ==============================================================================

def generate_keywords(config):
    """主生成函数"""
    
    main_name = config["main_name"]
    core_keywords = config["aliases"]
    features = config.get("features", [])  # 安全获取features，默认为空列表
    
    # 动态生成年份
    current_year = datetime.now().year
    year_intent = [f"{year}版" for year in range(current_year, current_year - 3, -1)]
    
    # 合并所有意图词
    all_intent_words = (
        DOWNLOAD_INTENT + VERSION_INTENT + year_intent + 
        TECH_SPEC_INTENT + RISK_VALUE_INTENT + PROBLEM_INTENT
    )
    
    generated_keywords = set()

    # --- 组合生成 ---
    # 组合模式1：[核心词] + [意图词] (两段式)
    for core in core_keywords:
        for intent in all_intent_words:
            generated_keywords.add(f"{core} {intent}")
            generated_keywords.add(f"{intent} {core}")
            if len(intent) <= 4:
                generated_keywords.add(f"{core}{intent}")

    # 组合模式2：[核心词] + [功能] + [意图词] (三段式，超级长尾)
    if features:
        for core in core_keywords:
            for feature in features:
                for intent in DOWNLOAD_INTENT + ["怎么用", "如何安装"]:
                    generated_keywords.add(f"{core} {feature} {intent}")
                    generated_keywords.add(f"{core} {intent} {feature}")

    # --- 最终清洗 ---
    # 排除所有不符合PC端、非官方的词
    exclusion_patterns = ["官方", "官网", "mac", "苹果", "ios", "安卓", "手机"]
    
    cleaned_keywords = set()
    for keyword in generated_keywords:
        # 使用正则表达式进行不区分大小写的匹配
        if not any(re.search(pattern, keyword, re.IGNORECASE) for pattern in exclusion_patterns):
            # 简单逻辑检查，避免 "微信 微信" 这样的重复
            parts = keyword.split()
            if len(parts) > 1 and len(set(parts)) == 1:
                continue
            cleaned_keywords.add(keyword)
            
    final_keyword_list = sorted(list(cleaned_keywords))

    # --- 创建DataFrame并输出 ---
    df = pd.DataFrame(final_keyword_list, columns=['Keyword'])

    output_filename = f"{main_name}_keywords.csv"
    df.to_csv(output_filename, index=False, encoding='utf_8_sig')
    
    print("="*50)
    print(f"🎉 关键词生成成功! 🎉")
    print(f"软件名称: {main_name}")
    print(f"生成关键词数量: {len(final_keyword_list)}")
    print(f"文件已保存为: {output_filename}")
    print("="*50)
    
    return df

# --- 执行脚本 ---
if __name__ == "__main__":
    keywords_df = generate_keywords(SOFTWARE_CONFIG)
    print("\n--- 关键词预览 (随机抽取20条) ---")
    print(keywords_df.sample(20).to_string())