// SEM关键词智能分组工具 - JavaScript模块
// 现代化的前端交互逻辑

class KeywordGrouper {
    constructor() {
        console.log('KeywordGrouper构造函数开始执行...');
        this.currentTaskId = null;
        this.processingTimer = null;
        this.charts = {};
        this.uploadedFile = null;
        this.processingStartTime = null;

        console.log('开始初始化...');
        this.init();
        console.log('KeywordGrouper构造函数执行完成');
    }

    init() {
        this.setupEventListeners();
        this.loadDefaultSettings();
        this.setupDragAndDrop();
        this.setupTabs();
        this.setupBaiduConverter();
        this.setupKeywordExpander();
    }

    setupEventListeners() {
        // 输入方式切换
        document.querySelectorAll('input[name="input-method"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handleInputMethodChange(e.target.value);
            });
        });

        // 文本输入相关
        const keywordsTextarea = document.getElementById('keywords-textarea');
        if (keywordsTextarea) {
            keywordsTextarea.addEventListener('input', (e) => {
                this.updateKeywordsCount(e.target.value);
            });
        }

        const clearKeywordsBtn = document.getElementById('clear-keywords-btn');
        if (clearKeywordsBtn) {
            clearKeywordsBtn.addEventListener('click', () => {
                this.clearKeywords();
            });
        }

        const sampleKeywordsBtn = document.getElementById('sample-keywords-btn');
        if (sampleKeywordsBtn) {
            sampleKeywordsBtn.addEventListener('click', () => {
                this.loadSampleKeywords();
            });
        }

        const processTextBtn = document.getElementById('process-text-btn');
        if (processTextBtn) {
            processTextBtn.addEventListener('click', (e) => {
                console.log('按钮被点击了');
                e.preventDefault();
                this.startTextProcessing();
            });
        } else {
            console.error('找不到process-text-btn按钮');
        }

        // 文件上传
        document.getElementById('upload-btn').addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        document.getElementById('file-input').addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelect(e.target.files[0]);
            }
        });

        // 配置变更
        document.getElementById('preset-select').addEventListener('change', (e) => {
            this.handlePresetChange(e.target.value);
        });

        document.getElementById('core-brand-option').addEventListener('change', (e) => {
            this.toggleManualCoreBrand(e.target.value === '手动指定');
        });

        document.getElementById('negative-words-option').addEventListener('change', (e) => {
            this.handleNegativeWordsOptionChange(e.target.value);
        });

        // 按钮事件
        document.getElementById('process-btn').addEventListener('click', () => {
            this.startProcessing();
        });

        document.getElementById('reupload-btn').addEventListener('click', () => {
            this.resetUpload();
        });

        document.getElementById('load-default-btn').addEventListener('click', () => {
            this.loadDefaultNegativeWords();
        });

        document.getElementById('retry-btn').addEventListener('click', () => {
            this.startProcessing();
        });

        // 导出按钮
        document.getElementById('export-complete-btn').addEventListener('click', () => {
            this.exportData('complete');
        });

        document.getElementById('export-campaign-btn').addEventListener('click', () => {
            this.exportData('campaign');
        });

        // 否定词计数
        document.getElementById('negative-words').addEventListener('input', (e) => {
            this.updateNegativeWordsCount(e.target.value);
        });
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0]);
            }
        });
    }

    loadDefaultSettings() {
        // 设置默认配置
        this.handlePresetChange('recommended');
        this.handleNegativeWordsOptionChange('modify');

        // 初始化关键词计数
        const keywordsTextarea = document.getElementById('keywords-textarea');
        if (keywordsTextarea) {
            this.updateKeywordsCount(keywordsTextarea.value);
        }

        // 默认显示文本输入区域
        this.handleInputMethodChange('text');

        // 验证关键元素是否存在
        this.validateElements();
    }

    // 新增：验证关键元素
    validateElements() {
        const requiredElements = [
            'keywords-textarea',
            'keywords-count',
            'process-text-btn',
            'text-input-area',
            'file-input-area',
            'clear-keywords-btn',
            'sample-keywords-btn'
        ];

        const missingElements = [];
        requiredElements.forEach(id => {
            if (!document.getElementById(id)) {
                missingElements.push(id);
            }
        });

        if (missingElements.length > 0) {
            console.error('缺少以下元素:', missingElements);
            this.showNotification('页面加载不完整，请刷新页面', 'error');
        } else {
            console.log('所有关键元素加载完成');
        }
    }

    handlePresetChange(preset) {
        const settings = {
            recommended: {
                brandAlgorithm: '智能词组识别',
                enableNgram: true,
                showFiltered: true,
                enableCoreRecommendation: true
            },
            custom: {
                // 保持当前设置
                brandAlgorithm: document.getElementById('brand-algorithm').value,
                enableNgram: document.getElementById('enable-ngram').checked,
                showFiltered: document.getElementById('show-filtered').checked,
                enableCoreRecommendation: document.getElementById('enable-core-recommendation').checked
            },
            fast: {
                brandAlgorithm: '传统单词识别',
                enableNgram: false,
                showFiltered: false,
                enableCoreRecommendation: true
            },
            precise: {
                brandAlgorithm: '智能词组识别',
                enableNgram: false,
                showFiltered: true,
                enableCoreRecommendation: true
            }
        };

        const config = settings[preset];
        if (config) {
            document.getElementById('brand-algorithm').value = config.brandAlgorithm;
            document.getElementById('enable-ngram').checked = config.enableNgram;
            document.getElementById('show-filtered').checked = config.showFiltered;
            document.getElementById('enable-core-recommendation').checked = config.enableCoreRecommendation;
        }
    }

    toggleManualCoreBrand(show) {
        const container = document.getElementById('manual-core-brand-container');
        if (show) {
            container.classList.remove('hidden');
        } else {
            container.classList.add('hidden');
        }
    }

    handleNegativeWordsOptionChange(option) {
        const container = document.getElementById('negative-words-container');
        const textarea = document.getElementById('negative-words');
        
        if (option === 'none' || option === 'default') {
            container.classList.add('hidden');
            textarea.value = '';
        } else {
            container.classList.remove('hidden');
            
            if (option === 'modify') {
                this.loadDefaultNegativeWords();
            } else if (option === 'custom') {
                textarea.value = '';
            }
        }
        
        this.updateNegativeWordsCount(textarea.value);
    }

    async loadDefaultNegativeWords() {
        try {
            const response = await fetch('/api/negative-words/default');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('negative-words').value = data.negative_words;
                this.updateNegativeWordsCount(data.negative_words);
                this.showNotification('默认否定词已加载', 'success');
            } else {
                this.showNotification('加载默认否定词失败', 'error');
            }
        } catch (error) {
            console.error('加载默认否定词失败:', error);
            this.showNotification('网络错误，请重试', 'error');
        }
    }

    updateNegativeWordsCount(text) {
        const words = text.split('\n').filter(word => word.trim().length > 0);
        document.getElementById('negative-words-count').textContent = `${words.length} 个否定词`;
    }

    // 新增：处理输入方式切换
    handleInputMethodChange(method) {
        const textInputArea = document.getElementById('text-input-area');
        const fileInputArea = document.getElementById('file-input-area');

        if (!textInputArea || !fileInputArea) {
            console.error('找不到输入区域元素');
            return;
        }

        if (method === 'text') {
            textInputArea.classList.remove('hidden');
            fileInputArea.classList.add('hidden');
            this.resetUpload(); // 清除文件上传状态
        } else {
            textInputArea.classList.add('hidden');
            fileInputArea.classList.remove('hidden');
        }
    }

    // 新增：更新关键词计数
    updateKeywordsCount(text) {
        const keywords = text.split('\n').filter(kw => kw.trim().length > 0);
        const countElement = document.getElementById('keywords-count');
        if (countElement) {
            countElement.textContent = keywords.length;
        }
    }

    // 新增：清空关键词
    clearKeywords() {
        document.getElementById('keywords-textarea').value = '';
        this.updateKeywordsCount('');
    }

    // 新增：加载示例关键词
    loadSampleKeywords() {
        const sampleKeywords = [
            'potplayer',
            'potplayer下载',
            'potplayer播放器',
            'potplayer官网',
            'potplayer安装',
            'potplayer中文版',
            'potplayer免费下载',
            'potplayer最新版',
            'potplayer绿色版',
            'potplayer破解版',
            'potplayer怎么用',
            'potplayer设置',
            'potplayer字幕',
            'potplayer快捷键',
            'potplayer皮肤'
        ].join('\n');

        document.getElementById('keywords-textarea').value = sampleKeywords;
        this.updateKeywordsCount(sampleKeywords);
        this.showNotification('示例关键词已加载', 'success');
    }

    // 新增：获取处理配置
    async getProcessingConfig() {
        const negativeWordsOption = document.getElementById('negative-words-option').value;
        let negativeWords = '';

        if (negativeWordsOption === 'default') {
            try {
                const defaultResponse = await fetch('/api/negative-words/default');
                const defaultData = await defaultResponse.json();
                negativeWords = defaultData.success ? defaultData.negative_words : '';
            } catch (error) {
                console.warn('获取默认否定词失败:', error);
            }
        } else if (negativeWordsOption === 'modify' || negativeWordsOption === 'custom') {
            negativeWords = document.getElementById('negative-words').value;
        }

        return {
            negative_words: negativeWords,
            brand_algorithm: document.getElementById('brand-algorithm').value,
            enable_ngram: document.getElementById('enable-ngram').checked,
            show_filtered: document.getElementById('show-filtered').checked,
            enable_core_recommendation: document.getElementById('enable-core-recommendation').checked,
            core_brand_option: document.getElementById('core-brand-option').value,
            manual_core_brand: document.getElementById('manual-core-brand').value
        };
    }

    // 新增：开始文本处理
    async startTextProcessing() {
        console.log('开始文本处理...');

        const keywordsTextarea = document.getElementById('keywords-textarea');
        if (!keywordsTextarea) {
            console.error('找不到关键词输入框');
            this.showNotification('页面元素加载异常，请刷新页面', 'error');
            return;
        }

        const keywordsText = keywordsTextarea.value.trim();
        console.log('关键词文本:', keywordsText);

        if (!keywordsText) {
            this.showNotification('请输入关键词', 'error');
            return;
        }

        const keywords = keywordsText.split('\n').filter(kw => kw.trim().length > 0);

        if (keywords.length === 0) {
            this.showNotification('未找到有效的关键词', 'error');
            return;
        }

        if (keywords.length > 10000) {
            this.showNotification('关键词数量过多，请控制在10000个以内', 'error');
            return;
        }

        this.hideError();
        this.hideResults();
        this.showProgress();

        this.processingStartTime = Date.now();
        this.startElapsedTimer();

        try {
            // 收集配置参数
            const config = await this.getProcessingConfig();

            // 创建FormData
            const formData = new FormData();
            formData.append('keywords_text', keywordsText);
            formData.append('negative_words', config.negative_words);
            formData.append('brand_algorithm', config.brand_algorithm);
            formData.append('enable_ngram', config.enable_ngram);
            formData.append('show_filtered', config.show_filtered);
            formData.append('enable_core_recommendation', config.enable_core_recommendation);
            formData.append('core_brand_option', config.core_brand_option);
            formData.append('manual_core_brand', config.manual_core_brand);

            console.log('发送的否定词:', config.negative_words);
            console.log('否定词长度:', config.negative_words.length);
            console.log('否定词行数:', config.negative_words.split('\n').length);
            console.log('否定词列表:', config.negative_words.split('\n').filter(w => w.trim()));

            const response = await fetch('/api/process-text-keywords', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.currentTaskId = data.task_id;
                this.showNotification(`开始处理 ${data.keywords_count} 个关键词`, 'success');
                this.startStatusPolling();
            } else {
                throw new Error(data.detail || '处理失败');
            }
        } catch (error) {
            console.error('文本处理失败:', error);
            this.showError(error.message || '处理失败，请重试');
            this.hideProgress();
            this.stopElapsedTimer();
        }
    }

    handleFileSelect(file) {
        // 验证文件类型
        const validTypes = ['.txt', '.csv', '.xlsx'];
        const fileName = file.name.toLowerCase();
        const isValid = validTypes.some(type => fileName.endsWith(type));
        
        if (!isValid) {
            this.showNotification('不支持的文件类型，请上传TXT、CSV或XLSX文件', 'error');
            return;
        }

        // 验证文件大小 (200MB)
        if (file.size > 200 * 1024 * 1024) {
            this.showNotification('文件过大，请上传小于200MB的文件', 'error');
            return;
        }

        this.uploadedFile = file;
        this.showUploadSuccess(file);
    }

    showUploadSuccess(file) {
        const defaultDiv = document.getElementById('upload-default');
        const successDiv = document.getElementById('upload-success');
        const fileInfo = document.getElementById('file-info');
        
        defaultDiv.classList.add('hidden');
        successDiv.classList.remove('hidden');
        
        const fileSizeKB = (file.size / 1024).toFixed(1);
        fileInfo.textContent = `${file.name} (${fileSizeKB} KB)`;
        
        // 添加成功动画
        successDiv.classList.add('animate-fadeIn');
    }

    resetUpload() {
        const defaultDiv = document.getElementById('upload-default');
        const successDiv = document.getElementById('upload-success');
        
        defaultDiv.classList.remove('hidden');
        successDiv.classList.add('hidden');
        
        this.uploadedFile = null;
        document.getElementById('file-input').value = '';
        
        this.hideResults();
        this.hideProgress();
        this.hideError();
    }

    async startProcessing() {
        if (!this.uploadedFile) {
            this.showNotification('请先上传文件', 'error');
            return;
        }

        this.hideError();
        this.hideResults();
        this.showProgress();
        
        this.processingStartTime = Date.now();
        this.startElapsedTimer();

        try {
            const formData = new FormData();
            formData.append('file', this.uploadedFile);
            
            // 获取配置
            const negativeWordsOption = document.getElementById('negative-words-option').value;
            let negativeWords = '';
            
            if (negativeWordsOption === 'default') {
                const defaultResponse = await fetch('/api/negative-words/default');
                const defaultData = await defaultResponse.json();
                negativeWords = defaultData.success ? defaultData.negative_words : '';
            } else if (negativeWordsOption === 'modify' || negativeWordsOption === 'custom') {
                negativeWords = document.getElementById('negative-words').value;
            }
            
            formData.append('negative_words', negativeWords);
            formData.append('brand_algorithm', document.getElementById('brand-algorithm').value);
            formData.append('enable_ngram', document.getElementById('enable-ngram').checked);
            formData.append('show_filtered', document.getElementById('show-filtered').checked);
            formData.append('enable_core_recommendation', document.getElementById('enable-core-recommendation').checked);
            formData.append('core_brand_option', document.getElementById('core-brand-option').value);
            formData.append('manual_core_brand', document.getElementById('manual-core-brand').value);

            const response = await fetch('/api/upload-keywords', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            
            if (data.success) {
                this.currentTaskId = data.task_id;
                this.showNotification('文件上传成功，开始处理...', 'success');
                this.startStatusPolling();
            } else {
                throw new Error(data.detail || '上传失败');
            }
        } catch (error) {
            console.error('处理失败:', error);
            this.showError(error.message || '处理失败，请重试');
            this.hideProgress();
            this.stopElapsedTimer();
        }
    }

    startStatusPolling() {
        this.processingTimer = setInterval(() => {
            this.checkStatus();
        }, 1000); // 每秒检查一次状态
    }

    async checkStatus() {
        if (!this.currentTaskId) return;

        try {
            const response = await fetch(`/api/status/${this.currentTaskId}`);
            const data = await response.json();
            
            this.updateProgress(data.progress, data.current_step, data.message);
            
            if (data.completed) {
                this.stopStatusPolling();
                this.stopElapsedTimer();
                
                if (data.error) {
                    this.showError(data.error);
                    this.hideProgress();
                } else if (data.result) {
                    this.loadResults();
                    this.hideProgress();
                }
            }
        } catch (error) {
            console.error('状态检查失败:', error);
            this.stopStatusPolling();
            this.showError('状态检查失败，请重试');
            this.hideProgress();
            this.stopElapsedTimer();
        }
    }

    stopStatusPolling() {
        if (this.processingTimer) {
            clearInterval(this.processingTimer);
            this.processingTimer = null;
        }
    }

    startElapsedTimer() {
        this.elapsedTimer = setInterval(() => {
            if (this.processingStartTime) {
                const elapsed = Math.floor((Date.now() - this.processingStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                document.getElementById('elapsed-time').textContent = timeStr;
            }
        }, 1000);
    }

    stopElapsedTimer() {
        if (this.elapsedTimer) {
            clearInterval(this.elapsedTimer);
            this.elapsedTimer = null;
        }
    }

    updateProgress(progress, step, message) {
        document.getElementById('progress-bar').style.width = `${progress}%`;
        document.getElementById('progress-percent').textContent = `${progress}%`;
        document.getElementById('progress-step').textContent = step;
        document.getElementById('progress-message').textContent = message;
    }

    showProgress() {
        document.getElementById('progress-container').classList.remove('hidden');
        document.getElementById('progress-container').classList.add('animate-fadeIn');
    }

    hideProgress() {
        document.getElementById('progress-container').classList.add('hidden');
    }

    async loadResults() {
        try {
            const response = await fetch(`/api/result/${this.currentTaskId}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayResults(data.result);
                this.showNotification('处理完成！', 'success');
            } else {
                throw new Error('获取结果失败');
            }
        } catch (error) {
            console.error('加载结果失败:', error);
            this.showError('加载结果失败，请重试');
        }
    }

    displayResults(result) {
        // 显示统计信息
        document.getElementById('total-keywords').textContent = result.total_keywords;
        document.getElementById('final-count').textContent = result.final_count;
        document.getElementById('group-count').textContent = result.group_count;
        document.getElementById('filtered-count').textContent = result.filtered_count;
        
        // 显示核心品牌词
        if (result.core_brand) {
            document.getElementById('core-brand-display').classList.remove('hidden');
            document.getElementById('core-brand-text').textContent = result.core_brand;
        }
        
        // 显示分组结果
        this.displayGroups(result.groups);
        
        // 显示图表
        this.displayCharts(result.groups);
        
        // 显示结果容器
        document.getElementById('results-container').classList.remove('hidden');
        document.getElementById('results-container').classList.add('animate-fadeIn');
    }

    displayGroups(groups) {
        const container = document.getElementById('groups-container');
        container.innerHTML = '';
        
        Object.entries(groups).forEach(([groupName, keywords]) => {
            const groupCard = this.createGroupCard(groupName, keywords);
            container.appendChild(groupCard);
        });
    }

    createGroupCard(groupName, keywords) {
        const card = document.createElement('div');
        card.className = 'group-card';
        
        const header = document.createElement('div');
        header.className = 'group-header';
        header.innerHTML = `
            <div class="flex justify-between items-center">
                <h4 class="font-semibold text-gray-900">${groupName}</h4>
                <div class="flex items-center space-x-2">
                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">${keywords.length} 个关键词</span>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                </div>
            </div>
        `;
        
        const content = document.createElement('div');
        content.className = 'group-content';
        
        const keywordsContainer = document.createElement('div');
        keywordsContainer.className = 'group-keywords';
        
        keywords.forEach(keyword => {
            const keywordItem = document.createElement('div');
            keywordItem.className = 'keyword-item';
            keywordItem.textContent = keyword;
            keywordsContainer.appendChild(keywordItem);
        });
        
        content.appendChild(keywordsContainer);
        
        // 添加点击事件
        header.addEventListener('click', () => {
            const icon = header.querySelector('i');
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('expanded');
                icon.style.transform = 'rotate(180deg)';
            }
        });
        
        card.appendChild(header);
        card.appendChild(content);
        
        return card;
    }

    displayCharts(groups) {
        this.createPieChart(groups);
        this.createBarChart(groups);
    }

    createPieChart(groups) {
        const ctx = document.getElementById('pie-chart').getContext('2d');
        
        if (this.charts.pie) {
            this.charts.pie.destroy();
        }
        
        const labels = Object.keys(groups);
        const data = Object.values(groups).map(keywords => keywords.length);
        const colors = [
            '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
            '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6b7280'
        ];
        
        this.charts.pie = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '分组关键词数量分布'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    createBarChart(groups) {
        const ctx = document.getElementById('bar-chart').getContext('2d');
        
        if (this.charts.bar) {
            this.charts.bar.destroy();
        }
        
        const labels = Object.keys(groups);
        const data = Object.values(groups).map(keywords => keywords.length);
        
        this.charts.bar = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '关键词数量',
                    data: data,
                    backgroundColor: '#3b82f6',
                    borderColor: '#1d4ed8',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各分组关键词数量'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    async exportData(type) {
        if (!this.currentTaskId) {
            this.showNotification('没有可导出的数据', 'error');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('export_type', type);
            
            const response = await fetch(`/api/export/${this.currentTaskId}`, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            
            if (data.success) {
                // 触发下载
                window.open(data.download_url, '_blank');
                this.showNotification(`导出成功！共 ${data.rows_count} 条数据`, 'success');
            } else {
                throw new Error('导出失败');
            }
        } catch (error) {
            console.error('导出失败:', error);
            this.showNotification('导出失败，请重试', 'error');
        }
    }

    showResults() {
        document.getElementById('results-container').classList.remove('hidden');
    }

    hideResults() {
        document.getElementById('results-container').classList.add('hidden');
    }

    showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-container').classList.remove('hidden');
        document.getElementById('error-container').classList.add('animate-fadeIn', 'error-shake');
    }

    hideError() {
        document.getElementById('error-container').classList.add('hidden');
        document.getElementById('error-container').classList.remove('animate-fadeIn', 'error-shake');
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
        
        // 设置样式
        if (type === 'success') {
            notification.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
            notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
        } else if (type === 'error') {
            notification.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
            notification.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
        } else {
            notification.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
            notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
        }
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
            notification.classList.add('translate-x-0');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('translate-x-0');
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // ==================== 标签页功能 ====================

    setupTabs() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 确保获取按钮元素而不是子元素
                const clickedButton = e.currentTarget;
                const targetId = clickedButton.id.replace('tab-', 'tab-content-');
                
                // 切换按钮状态
                tabButtons.forEach(btn => {
                    btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });
                
                clickedButton.classList.add('active', 'border-blue-500', 'text-blue-600');
                clickedButton.classList.remove('border-transparent', 'text-gray-500');
                
                // 切换内容显示
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                const targetContent = document.getElementById(targetId);
                if (targetContent) {
                    targetContent.classList.remove('hidden');
                }
                
                // 如果是百度转换标签页，加载模板信息
                if (targetId === 'tab-content-baidu-convert') {
                    this.loadBaiduTemplateInfo();
                }
            });
        });
    }

    // ==================== 百度转Bing功能 ====================

    setupBaiduConverter() {
        this.currentBaiduTaskId = null;
        this.baiduStatusInterval = null;
        this.baiduUploadedFile = null;
        this.baiduStartTime = null;
        this.baiduElapsedTimer = null;
        
        this.setupBaiduEventListeners();
        this.loadBaiduTemplateInfo();
    }

    setupBaiduEventListeners() {
        // 百度文件上传
        const uploadBtn = document.getElementById('baidu-upload-btn');
        const fileInput = document.getElementById('baidu-file-input');
        
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                fileInput.click();
            });
        }

        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.handleBaiduFileSelect(e.target.files[0]);
                }
            });
        }

        // 百度拖拽上传
        this.setupBaiduDragAndDrop();

        // 百度转换按钮
        const convertBtn = document.getElementById('baidu-convert-btn');
        if (convertBtn) {
            convertBtn.addEventListener('click', () => {
                this.startBaiduConversion();
            });
        }

        const previewBtn = document.getElementById('baidu-preview-btn');
        if (previewBtn) {
            previewBtn.addEventListener('click', () => {
                this.previewBaiduFile();
            });
        }

        const reuploadBtn = document.getElementById('baidu-reupload-btn');
        if (reuploadBtn) {
            reuploadBtn.addEventListener('click', () => {
                this.resetBaiduUpload();
            });
        }

        const exportBtn = document.getElementById('baidu-export-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportBaiduResult();
            });
        }

        const retryBtn = document.getElementById('baidu-retry-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.startBaiduConversion();
            });
        }
    }

    setupBaiduDragAndDrop() {
        const uploadArea = document.getElementById('baidu-upload-area');
        if (!uploadArea) return;
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('border-blue-400');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-blue-400');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-blue-400');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleBaiduFileSelect(files[0]);
            }
        });
    }

    async loadBaiduTemplateInfo() {
        try {
            const response = await fetch('/api/baidu-template-info');
            const data = await response.json();
            
            if (data.success) {
                const uploadTemplate = document.getElementById('baidu-upload-template');
                const exportTemplate = document.getElementById('baidu-export-template');
                
                if (uploadTemplate) {
                    uploadTemplate.textContent = data.template_info.upload_template;
                }
                if (exportTemplate) {
                    exportTemplate.textContent = data.template_info.export_template;
                }
            }
        } catch (error) {
            console.error('加载模板信息失败:', error);
        }
    }

    handleBaiduFileSelect(file) {
        // 验证文件类型
        const validTypes = ['.csv', '.xlsx'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!validTypes.includes(fileExtension)) {
            this.showNotification('不支持的文件类型，请上传CSV或XLSX文件', 'error');
            return;
        }

        // 验证文件大小 (200MB)
        if (file.size > 200 * 1024 * 1024) {
            this.showNotification('文件过大，请上传小于200MB的文件', 'error');
            return;
        }

        this.baiduUploadedFile = file;
        this.showBaiduUploadSuccess(file);
    }

    showBaiduUploadSuccess(file) {
        const fileSize = (file.size / 1024).toFixed(1);
        const fileInfo = document.getElementById('baidu-file-info');
        
        if (fileInfo) {
            fileInfo.textContent = `${file.name} (${fileSize} KB)`;
        }
        
        // 切换显示状态
        const uploadDefault = document.getElementById('baidu-upload-default');
        const uploadSuccess = document.getElementById('baidu-upload-success');
        
        if (uploadDefault) uploadDefault.classList.add('hidden');
        if (uploadSuccess) uploadSuccess.classList.remove('hidden');
        
        // 隐藏之前的结果
        this.hideBaiduResults();
        this.hideBaiduError();
    }

    resetBaiduUpload() {
        this.baiduUploadedFile = null;
        const fileInput = document.getElementById('baidu-file-input');
        if (fileInput) {
            fileInput.value = '';
        }
        
        // 重置显示状态
        const uploadDefault = document.getElementById('baidu-upload-default');
        const uploadSuccess = document.getElementById('baidu-upload-success');
        const filePreview = document.getElementById('baidu-file-preview');
        
        if (uploadDefault) uploadDefault.classList.remove('hidden');
        if (uploadSuccess) uploadSuccess.classList.add('hidden');
        if (filePreview) filePreview.classList.add('hidden');
        
        // 隐藏结果和错误
        this.hideBaiduResults();
        this.hideBaiduError();
        this.hideBaiduProgress();
    }

    async previewBaiduFile() {
        if (!this.baiduUploadedFile) {
            this.showNotification('请先上传文件', 'error');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('file', this.baiduUploadedFile);

            const response = await fetch('/api/preview-baidu-file', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            
            if (data.success) {
                this.displayBaiduPreview(data);
                const filePreview = document.getElementById('baidu-file-preview');
                if (filePreview) {
                    filePreview.classList.remove('hidden');
                }
            } else {
                this.showNotification(data.error || '预览失败', 'error');
            }
        } catch (error) {
            console.error('预览文件失败:', error);
            this.showNotification('网络错误，请重试', 'error');
        }
    }

    displayBaiduPreview(data) {
        const infoDiv = document.getElementById('baidu-preview-info');
        if (infoDiv) {
            infoDiv.textContent = `数据行数：${data.total_rows}，预览前${data.preview_rows}行`;
        }
        
        const tableDiv = document.getElementById('baidu-preview-table');
        if (!tableDiv) return;
        
        let tableHTML = '<table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr>';
        
        // 表头
        data.columns.forEach(col => {
            tableHTML += `<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">${col}</th>`;
        });
        tableHTML += '</tr></thead><tbody class="bg-white divide-y divide-gray-200">';
        
        // 数据行
        data.data.forEach(row => {
            tableHTML += '<tr>';
            data.columns.forEach(col => {
                const cellValue = row[col] || '';
                tableHTML += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${cellValue}</td>`;
            });
            tableHTML += '</tr>';
        });
        
        tableHTML += '</tbody></table>';
        tableDiv.innerHTML = tableHTML;
    }

    async startBaiduConversion() {
        if (!this.baiduUploadedFile) {
            this.showNotification('请先上传文件', 'error');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('file', this.baiduUploadedFile);
            
            const urlParsingCheckbox = document.getElementById('baidu-enable-url-parsing');
            const dataCleaningCheckbox = document.getElementById('baidu-enable-data-cleaning');
            const preserveDataCheckbox = document.getElementById('baidu-preserve-original-data');
            
            formData.append('enable_url_parsing', urlParsingCheckbox ? urlParsingCheckbox.checked : true);
            formData.append('enable_data_cleaning', dataCleaningCheckbox ? dataCleaningCheckbox.checked : true);
            formData.append('preserve_original_data', preserveDataCheckbox ? preserveDataCheckbox.checked : false);

            const response = await fetch('/api/upload-baidu-file', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            
            if (data.success) {
                this.currentBaiduTaskId = data.task_id;
                this.showNotification('转换已开始', 'success');
                this.showBaiduProgress();
                this.startBaiduStatusPolling();
                this.startBaiduElapsedTimer();
                this.hideBaiduError();
            } else {
                this.showBaiduError(data.detail || '转换失败');
            }
        } catch (error) {
            console.error('开始转换失败:', error);
            this.showBaiduError('网络错误，请重试');
        }
    }

    startBaiduStatusPolling() {
        this.baiduStatusInterval = setInterval(() => {
            this.checkBaiduStatus();
        }, 1000);
    }

    async checkBaiduStatus() {
        if (!this.currentBaiduTaskId) return;

        try {
            const response = await fetch(`/api/status/${this.currentBaiduTaskId}`);
            const data = await response.json();
            
            if (data.success) {
                this.updateBaiduProgress(data.progress, data.current_step, data.message);
                
                if (data.completed) {
                    this.stopBaiduStatusPolling();
                    this.stopBaiduElapsedTimer();
                    this.loadBaiduResults();
                } else if (data.error) {
                    this.stopBaiduStatusPolling();
                    this.stopBaiduElapsedTimer();
                    this.showBaiduError(data.error);
                }
            }
        } catch (error) {
            console.error('检查状态失败:', error);
        }
    }

    stopBaiduStatusPolling() {
        if (this.baiduStatusInterval) {
            clearInterval(this.baiduStatusInterval);
            this.baiduStatusInterval = null;
        }
    }

    startBaiduElapsedTimer() {
        this.baiduStartTime = Date.now();
        this.baiduElapsedTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.baiduStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            
            const elapsedTimeElement = document.getElementById('baidu-elapsed-time');
            if (elapsedTimeElement) {
                elapsedTimeElement.textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }

    stopBaiduElapsedTimer() {
        if (this.baiduElapsedTimer) {
            clearInterval(this.baiduElapsedTimer);
            this.baiduElapsedTimer = null;
        }
    }

    updateBaiduProgress(progress, step, message) {
        const progressPercent = document.getElementById('baidu-progress-percent');
        const progressBar = document.getElementById('baidu-progress-bar');
        const progressStep = document.getElementById('baidu-progress-step');
        const progressMessage = document.getElementById('baidu-progress-message');
        
        if (progressPercent) progressPercent.textContent = `${progress}%`;
        if (progressBar) progressBar.style.width = `${progress}%`;
        if (progressStep) progressStep.textContent = step;
        if (progressMessage) progressMessage.textContent = message;
    }

    showBaiduProgress() {
        const progressContainer = document.getElementById('baidu-progress-container');
        if (progressContainer) {
            progressContainer.classList.remove('hidden');
        }
    }

    hideBaiduProgress() {
        const progressContainer = document.getElementById('baidu-progress-container');
        if (progressContainer) {
            progressContainer.classList.add('hidden');
        }
    }

    async loadBaiduResults() {
        try {
            const response = await fetch(`/api/status/${this.currentBaiduTaskId}`);
            const data = await response.json();
            
            if (data.success && data.result) {
                this.displayBaiduResults(data.result);
                this.hideBaiduProgress();
                this.showBaiduResults();
                this.showNotification('转换完成！', 'success');
            } else {
                this.showBaiduError(data.error || '加载结果失败');
            }
        } catch (error) {
            console.error('加载结果失败:', error);
            this.showBaiduError('网络错误，请重试');
        }
    }

    displayBaiduResults(result) {
        // 显示统计信息
        const originalRows = document.getElementById('baidu-original-rows');
        const convertedRows = document.getElementById('baidu-converted-rows');
        const columnsCount = document.getElementById('baidu-columns-count');
        const conversionRate = document.getElementById('baidu-conversion-rate');
        
        if (originalRows) originalRows.textContent = result.original_rows;
        if (convertedRows) convertedRows.textContent = result.total_rows;
        if (columnsCount) columnsCount.textContent = result.columns.length;
        
        if (conversionRate) {
            const rate = result.original_rows > 0 ? 
                Math.round((result.total_rows / result.original_rows) * 100) : 0;
            conversionRate.textContent = `${rate}%`;
        }
        
        // 显示结果预览
        this.displayBaiduResultPreview(result);
    }

    displayBaiduResultPreview(result) {
        const previewDiv = document.getElementById('baidu-result-preview');
        if (!previewDiv) return;
        
        let tableHTML = '<table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr>';
        
        // 表头
        result.columns.forEach(col => {
            tableHTML += `<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">${col}</th>`;
        });
        tableHTML += '</tr></thead><tbody class="bg-white divide-y divide-gray-200">';
        
        // 数据行（只显示前10行）
        const previewData = result.result_df.slice(0, 10);
        previewData.forEach(row => {
            tableHTML += '<tr>';
            result.columns.forEach(col => {
                const cellValue = row[col] || '';
                tableHTML += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${cellValue}</td>`;
            });
            tableHTML += '</tr>';
        });
        
        tableHTML += '</tbody></table>';
        previewDiv.innerHTML = tableHTML;
    }

    async exportBaiduResult() {
        if (!this.currentBaiduTaskId) {
            this.showNotification('没有可导出的结果', 'error');
            return;
        }

        try {
            const response = await fetch(`/api/export-baidu-result/${this.currentBaiduTaskId}`, {
                method: 'POST'
            });

            const data = await response.json();
            
            if (data.success) {
                // 触发下载
                window.open(data.download_url, '_blank');
                this.showNotification(`🎉 导出成功！共 ${data.rows_count} 行数据已保存到Excel文件`, 'success');
                
                // 显示导出成功提示
                const exportBtn = document.getElementById('baidu-export-btn');
                if (exportBtn) {
                    const successDiv = document.createElement('div');
                    successDiv.className = 'export-success';
                    successDiv.innerHTML = `
                        <i class="fas fa-check-circle mr-2"></i>
                        导出成功！文件已下载到您的设备
                    `;
                    exportBtn.parentNode.appendChild(successDiv);
                    
                    // 3秒后移除提示
                    setTimeout(() => {
                        successDiv.remove();
                    }, 3000);
                }
            } else {
                this.showNotification(data.detail || '导出失败', 'error');
            }
        } catch (error) {
            console.error('导出失败:', error);
            this.showNotification('网络错误，请重试', 'error');
        }
    }

    showBaiduResults() {
        const resultsContainer = document.getElementById('baidu-results-container');
        if (resultsContainer) {
            resultsContainer.classList.remove('hidden');
            resultsContainer.classList.add('results-container');
            
            // 高亮导出按钮
            setTimeout(() => {
                const exportBtn = document.getElementById('baidu-export-btn');
                if (exportBtn) {
                    exportBtn.classList.add('export-button-highlight');
                    
                    // 5秒后停止闪烁
                    setTimeout(() => {
                        exportBtn.classList.remove('export-button-highlight');
                    }, 5000);
                }
            }, 1000);
        }
    }

    hideBaiduResults() {
        const resultsContainer = document.getElementById('baidu-results-container');
        if (resultsContainer) {
            resultsContainer.classList.add('hidden');
        }
    }

    showBaiduError(message) {
        const errorMessage = document.getElementById('baidu-error-message');
        const errorContainer = document.getElementById('baidu-error-container');
        
        if (errorMessage) errorMessage.textContent = message;
        if (errorContainer) errorContainer.classList.remove('hidden');
        
        this.hideBaiduProgress();
    }

    hideBaiduError() {
        const errorContainer = document.getElementById('baidu-error-container');
        if (errorContainer) {
            errorContainer.classList.add('hidden');
        }
    }

    // ==================== 关键词扩展功能 ====================
    
    setupKeywordExpander() {
        // 初始化关键词扩展相关变量
        this.expanderUploadedTemplate = null;
        this.expanderUploadedKeywords = null;
        this.expanderCurrentTaskId = null;
        this.expanderProcessingTimer = null;
        this.expanderProcessingStartTime = null;
        
        this.setupExpanderEventListeners();
        this.setupExpanderDragAndDrop();
        this.loadExpanderDefaults();
    }

    setupExpanderEventListeners() {
        // 模板上传
        const templateUploadBtn = document.getElementById('expand-template-upload-btn');
        if (templateUploadBtn) {
            templateUploadBtn.addEventListener('click', () => {
                document.getElementById('expand-template-file').click();
            });
        }

        const templateInput = document.getElementById('expand-template-file');
        if (templateInput) {
            templateInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.handleExpanderTemplateSelect(e.target.files[0]);
                }
            });
        }

        // 关键词文件上传
        const keywordsUploadBtn = document.getElementById('expand-keywords-upload-area');
        if (keywordsUploadBtn) {
            keywordsUploadBtn.addEventListener('click', () => {
                document.getElementById('expand-keywords-file').click();
            });
        }

        const keywordsInput = document.getElementById('expand-keywords-file');
        if (keywordsInput) {
            keywordsInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.handleExpanderKeywordsSelect(e.target.files[0]);
                }
            });
        }

        // 自定义模板选择
        const customTemplateCheckbox = document.getElementById('expand-use-custom-template');
        if (customTemplateCheckbox) {
            customTemplateCheckbox.addEventListener('change', (e) => {
                this.toggleExpanderTemplateUpload(e.target.checked);
                this.updateExpanderButtonState();
            });
        }

        // 输入方式切换
        const inputMethods = document.querySelectorAll('input[name="expand-input-method"]');
        inputMethods.forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handleExpanderInputMethodChange(e.target.value);
                this.updateExpanderButtonState();
            });
        });

        // 开始扩展按钮
        const startBtn = document.getElementById('expand-start-button');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.startKeywordExpansion();
            });
        }

        // 重试按钮
        const retryBtn = document.getElementById('expand-retry-button');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.startKeywordExpansion();
            });
        }

        // 导出按钮
        const exportBtn = document.getElementById('expand-export-button');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportExpanderResult();
            });
        }

        // 关键词计数
        const manualKeywords = document.getElementById('expand-keywords-text');
        if (manualKeywords) {
            manualKeywords.addEventListener('input', (e) => {
                this.updateExpanderKeywordsCount(e.target.value);
                this.updateExpanderButtonState();
            });
            // 初始化计数
            this.updateExpanderKeywordsCount(manualKeywords.value);
        }
    }

    setupExpanderDragAndDrop() {
        // 模板文件拖拽
        const templateArea = document.getElementById('expand-template-upload-area');
        if (templateArea) {
            templateArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                templateArea.classList.add('dragover');
            });

            templateArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                templateArea.classList.remove('dragover');
            });

            templateArea.addEventListener('drop', (e) => {
                e.preventDefault();
                templateArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleExpanderTemplateSelect(files[0]);
                }
            });
        }

        // 关键词文件拖拽
        const keywordsArea = document.getElementById('expand-keywords-upload-area');
        if (keywordsArea) {
            keywordsArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                keywordsArea.classList.add('dragover');
            });

            keywordsArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                keywordsArea.classList.remove('dragover');
            });

            keywordsArea.addEventListener('drop', (e) => {
                e.preventDefault();
                keywordsArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleExpanderKeywordsSelect(files[0]);
                }
            });
        }
    }

    loadExpanderDefaults() {
        // 设置默认值
        this.toggleExpanderTemplateUpload(false);
        this.handleExpanderInputMethodChange('manual');
        
        const defaultKeywords = 'potplayer\npotplayer播放器\npotplayer下载';
        const manualKeywords = document.getElementById('expand-keywords-text');
        if (manualKeywords) {
            manualKeywords.value = defaultKeywords;
            this.updateExpanderKeywordsCount(defaultKeywords);
        }
        
        // 更新配置摘要和按钮状态
        this.updateExpanderButtonState();
    }

    toggleExpanderTemplateUpload(show) {
        const customTemplateSection = document.getElementById('expand-custom-template-section');
        const defaultTemplateStatus = document.getElementById('expand-default-template-status');
        
        if (show) {
            if (customTemplateSection) customTemplateSection.classList.remove('hidden');
            if (defaultTemplateStatus) defaultTemplateStatus.classList.add('hidden');
        } else {
            if (customTemplateSection) customTemplateSection.classList.add('hidden');
            if (defaultTemplateStatus) defaultTemplateStatus.classList.remove('hidden');
        }
    }

    handleExpanderInputMethodChange(method) {
        const manualSection = document.getElementById('expand-manual-input');
        const uploadSection = document.getElementById('expand-file-input');
        
        if (method === 'manual') {
            if (manualSection) manualSection.classList.remove('hidden');
            if (uploadSection) uploadSection.classList.add('hidden');
        } else {
            if (manualSection) manualSection.classList.add('hidden');
            if (uploadSection) uploadSection.classList.remove('hidden');
        }
    }

    handleExpanderTemplateSelect(file) {
        // 验证文件类型
        if (!file.name.toLowerCase().endsWith('.xlsx')) {
            this.showNotification('请选择Excel文件(.xlsx)', 'error');
            return;
        }

        // 验证文件大小
        const maxSize = 200 * 1024 * 1024; // 200MB
        if (file.size > maxSize) {
            this.showNotification('文件大小不能超过200MB', 'error');
            return;
        }

        this.expanderUploadedTemplate = file;
        this.showExpanderTemplateSuccess(file);
    }

    showExpanderTemplateSuccess(file) {
        const statusDiv = document.getElementById('expand-template-status');
        if (statusDiv) {
            statusDiv.innerHTML = `
                <div class="flex items-center text-green-600 text-sm">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>已上传模板：<strong>${file.name}</strong> (${(file.size / 1024).toFixed(1)} KB)</span>
                </div>
            `;
        }
        this.showNotification('模板文件上传成功', 'success');
        this.updateExpanderButtonState();
    }

    handleExpanderKeywordsSelect(file) {
        // 验证文件类型
        const validTypes = ['txt', 'csv', 'xlsx', 'xls'];
        const fileExt = file.name.split('.').pop().toLowerCase();
        if (!validTypes.includes(fileExt)) {
            this.showNotification('请选择TXT、CSV或Excel文件', 'error');
            return;
        }

        // 验证文件大小
        const maxSize = 200 * 1024 * 1024; // 200MB
        if (file.size > maxSize) {
            this.showNotification('文件大小不能超过200MB', 'error');
            return;
        }

        this.expanderUploadedKeywords = file;
        this.showExpanderKeywordsSuccess(file);
    }

    showExpanderKeywordsSuccess(file) {
        const statusDiv = document.getElementById('expand-keywords-file-status');
        if (statusDiv) {
            statusDiv.innerHTML = `
                <div class="flex items-center text-green-600 text-sm">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>已上传文件：<strong>${file.name}</strong> (${(file.size / 1024).toFixed(1)} KB)</span>
                </div>
            `;
        }
        this.showNotification('关键词文件上传成功', 'success');
        this.updateExpanderButtonState();
    }

    updateExpanderKeywordsCount(text) {
        const keywords = text.split('\n').filter(kw => kw.trim().length > 0);
        const countElement = document.getElementById('expand-manual-keywords-count');
        if (countElement) {
            countElement.textContent = `${keywords.length} 个关键词`;
        }
    }

    updateExpanderButtonState() {
        const startButton = document.getElementById('expand-start-button');
        const buttonText = document.getElementById('expand-button-text');
        
        if (!startButton || !buttonText) return;

        const config = this.validateExpanderConfig();
        
        if (config.valid) {
            startButton.disabled = false;
            startButton.classList.remove('bg-gray-300', 'cursor-not-allowed');
            startButton.classList.add('bg-orange-600', 'hover:bg-orange-700');
            buttonText.textContent = '开始扩展关键词';
            
            // 更新配置摘要
            this.displayExpanderSummary(config);
        } else {
            startButton.disabled = true;
            startButton.classList.add('bg-gray-300', 'cursor-not-allowed');
            startButton.classList.remove('bg-orange-600', 'hover:bg-orange-700');
            buttonText.textContent = config.message || '请完成配置';
            
            // 清空配置摘要
            const summaryText = document.getElementById('expand-summary-text');
            if (summaryText) {
                summaryText.textContent = '请完成配置';
            }
        }
    }

    async startKeywordExpansion() {
        try {
            // 重置状态
            this.hideExpanderError();
            this.hideExpanderResults();
            
            // 验证配置
            const config = this.validateExpanderConfig();
            if (!config.valid) {
                this.showExpanderError(config.message);
                return;
            }

            // 显示配置摘要
            this.displayExpanderSummary(config);

            // 上传文件和开始处理
            await this.uploadExpanderFiles(config);

        } catch (error) {
            console.error('开始关键词扩展失败:', error);
            this.showExpanderError('启动失败，请重试');
        }
    }

    validateExpanderConfig() {
        const useCustomTemplate = document.getElementById('expand-use-custom-template');
        const inputMethodRadio = document.querySelector('input[name="expand-input-method"]:checked');
        
        if (!inputMethodRadio) {
            return { valid: false, message: '页面配置错误，请刷新页面' };
        }

        const useCustomTemplateChecked = useCustomTemplate ? useCustomTemplate.checked : false;
        const inputMethod = inputMethodRadio.value;
        
        // 验证模板
        if (useCustomTemplateChecked && !this.expanderUploadedTemplate) {
            return { valid: false, message: '请上传自定义模板文件' };
        }

        // 验证关键词
        if (inputMethod === 'manual') {
            const manualKeywords = document.getElementById('expand-keywords-text');
            if (!manualKeywords || !manualKeywords.value.trim()) {
                return { valid: false, message: '请输入基础关键词' };
            }
        } else {
            if (!this.expanderUploadedKeywords) {
                return { valid: false, message: '请上传关键词文件' };
            }
        }

        return { valid: true };
    }

    displayExpanderSummary(config) {
        const summaryDiv = document.getElementById('expand-config-summary');
        if (!summaryDiv) return;

        const useCustomTemplate = document.getElementById('expand-use-custom-template');
        const inputMethodRadio = document.querySelector('input[name="expand-input-method"]:checked');
        const matchType = document.getElementById('expand-match-type');
        const bidPrice = document.getElementById('expand-bid-price');
        
        if (!inputMethodRadio) {
            return;
        }

        const useCustomTemplateChecked = useCustomTemplate ? useCustomTemplate.checked : false;
        const inputMethod = inputMethodRadio.value;
        
        let keywordCount = 0;
        if (inputMethod === 'manual') {
            const manualKeywords = document.getElementById('expand-keywords-text');
            if (manualKeywords) {
                keywordCount = manualKeywords.value.trim().split('\n').filter(kw => kw.trim().length > 0).length;
            }
        } else {
            keywordCount = '文件上传';
        }

        const templateInfo = useCustomTemplateChecked ? '自定义模板' : '默认模板';
        const matchTypeValue = matchType ? matchType.value : 'Exact';
        const bidPriceValue = bidPrice ? bidPrice.value : '0.39';

        const summaryText = document.getElementById('expand-summary-text');
        if (summaryText) {
            summaryText.textContent = `${templateInfo} | ${keywordCount}个关键词 | ${matchTypeValue}匹配 | ${bidPriceValue}出价`;
        }
    }

    async uploadExpanderFiles(config) {
        // 准备JSON数据
        const requestData = {
            campaign_name: '',
            ad_group_name: '',
            match_type: '',
            bid_price: '',
            final_url: '',
            keywords: [],
            use_custom_template: false,
            template_task_id: null
        };
        
        // 基础配置
        const campaignName = document.getElementById('expand-campaign-name');
        const adGroupName = document.getElementById('expand-ad-group-name');
        const matchType = document.getElementById('expand-match-type');
        const bidPrice = document.getElementById('expand-bid-price');
        const finalUrl = document.getElementById('expand-final-url');
        
        // 设置基础配置数据
        requestData.campaign_name = campaignName ? campaignName.value : '第三方软件-potplayer';
        requestData.ad_group_name = adGroupName ? adGroupName.value : '错拼词';
        requestData.match_type = matchType ? matchType.value : 'Exact';
        requestData.bid_price = bidPrice ? bidPrice.value : '0.39';
        requestData.final_url = finalUrl ? finalUrl.value : 'https://sem.duba.net/sem/dseek/f255.html?sfrom=196&TFT=16&keyID=144553';
        
        // 处理模板文件
        const useCustomTemplate = document.getElementById('expand-use-custom-template');
        if (useCustomTemplate && useCustomTemplate.checked && this.expanderUploadedTemplate) {
            const templateFormData = new FormData();
            templateFormData.append('template_file', this.expanderUploadedTemplate);
            
            const templateResponse = await fetch('/api/upload-expander-template', {
                method: 'POST',
                body: templateFormData
            });
            
            if (!templateResponse.ok) {
                throw new Error('模板上传失败');
            }
            
            const templateData = await templateResponse.json();
            requestData.use_custom_template = true;
            requestData.template_task_id = templateData.task_id;
        }
        
        // 处理关键词
        const inputMethodRadio = document.querySelector('input[name="expand-input-method"]:checked');
        if (!inputMethodRadio) {
            throw new Error('请选择输入方式');
        }

        const inputMethod = inputMethodRadio.value;
        if (inputMethod === 'manual') {
            const manualKeywords = document.getElementById('expand-keywords-text');
            if (manualKeywords) {
                const keywordsText = manualKeywords.value.trim();
                if (keywordsText) {
                    requestData.keywords = keywordsText.split('\n').map(kw => kw.trim()).filter(kw => kw.length > 0);
                }
            }
        } else {
            // 上传关键词文件
            const keywordsFormData = new FormData();
            keywordsFormData.append('keywords_file', this.expanderUploadedKeywords);
            
            const keywordsResponse = await fetch('/api/upload-keywords-file', {
                method: 'POST',
                body: keywordsFormData
            });
            
            if (!keywordsResponse.ok) {
                throw new Error('关键词文件上传失败');
            }
            
            const keywordsData = await keywordsResponse.json();
            requestData.keywords = keywordsData.keywords;
        }

        // 检查关键词是否为空
        if (!requestData.keywords || requestData.keywords.length === 0) {
            throw new Error('未找到有效的关键词');
        }

        // 开始扩展
        const response = await fetch('/api/expand-keywords-async', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '扩展启动失败');
        }

        const result = await response.json();
        
        if (result.success) {
            this.expanderCurrentTaskId = result.task_id;
            this.showExpanderProgress();
            this.startExpanderStatusPolling();
            this.startExpanderElapsedTimer();
            this.showNotification('关键词扩展已启动', 'success');
        } else {
            this.showExpanderError(result.error || '启动失败');
        }
    }

    startExpanderStatusPolling() {
        this.expanderProcessingTimer = setInterval(() => {
            this.checkExpanderStatus();
        }, 2000);
    }

    async checkExpanderStatus() {
        try {
            const response = await fetch(`/api/status/${this.expanderCurrentTaskId}`);
            const data = await response.json();

            if (data.success) {
                this.updateExpanderProgress(data.progress, data.current_step, data.message);

                if (data.completed) {
                    this.stopExpanderStatusPolling();
                    this.stopExpanderElapsedTimer();
                    await this.loadExpanderResults();
                } else if (data.error) {
                    this.stopExpanderStatusPolling();
                    this.stopExpanderElapsedTimer();
                    this.hideExpanderProgress();
                    this.showExpanderError(data.error || '扩展失败');
                }
            }
        } catch (error) {
            console.error('检查扩展状态失败:', error);
        }
    }

    stopExpanderStatusPolling() {
        if (this.expanderProcessingTimer) {
            clearInterval(this.expanderProcessingTimer);
            this.expanderProcessingTimer = null;
        }
    }

    startExpanderElapsedTimer() {
        this.expanderProcessingStartTime = Date.now();
        this.expanderElapsedTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.expanderProcessingStartTime) / 1000);
            const elapsedElement = document.getElementById('expand-progress-time');
            if (elapsedElement) {
                elapsedElement.textContent = `⏱️ 已用时：${elapsed}秒`;
            }
        }, 1000);
    }

    stopExpanderElapsedTimer() {
        if (this.expanderElapsedTimer) {
            clearInterval(this.expanderElapsedTimer);
            this.expanderElapsedTimer = null;
        }
    }

    updateExpanderProgress(progress, step, message) {
        const progressBar = document.getElementById('expand-progress-bar');
        const progressText = document.getElementById('expand-progress-percentage');
        const stepText = document.getElementById('expand-progress-step');
        const messageText = document.getElementById('expand-progress-message');

        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }

        if (progressText) {
            progressText.textContent = `${progress}%`;
        }

        if (stepText) {
            stepText.textContent = `${step}`;
        }

        if (messageText) {
            messageText.textContent = message;
        }
    }

    showExpanderProgress() {
        const progressSection = document.getElementById('expand-progress-section');
        if (progressSection) {
            progressSection.classList.remove('hidden');
        }
    }

    hideExpanderProgress() {
        const progressSection = document.getElementById('expand-progress-section');
        if (progressSection) {
            progressSection.classList.add('hidden');
        }
    }

    async loadExpanderResults() {
        try {
            const response = await fetch(`/api/status/${this.expanderCurrentTaskId}`);
            const data = await response.json();

            if (data.success && data.result) {
                this.hideExpanderProgress();
                this.displayExpanderResults(data.result);
                this.showExpanderResults();
                this.showNotification('关键词扩展完成！', 'success');
            } else {
                this.showExpanderError('无法加载扩展结果');
            }
        } catch (error) {
            console.error('加载扩展结果失败:', error);
            this.showExpanderError('加载结果失败，请重试');
        }
    }

    displayExpanderResults(result) {
        // 显示统计信息
        const originalStat = document.getElementById('expand-stat-original');
        const expandedStat = document.getElementById('expand-stat-expanded');
        const finalStat = document.getElementById('expand-stat-final');
        const rateStat = document.getElementById('expand-stat-rate');
        
        // 检查是否有statistics字段
        const stats = result.statistics || result;
        
        if (originalStat) originalStat.textContent = stats.original_count || 0;
        if (expandedStat) expandedStat.textContent = stats.expanded_count || 0;
        if (finalStat) finalStat.textContent = stats.final_rows || 0;
        if (rateStat) rateStat.textContent = `${stats.expansion_rate || 0}x`;

        // 显示结果预览
        const previewData = result.preview || result.preview_data;
        if (previewData && previewData.length > 0) {
            this.displayExpanderPreview(previewData);
        }
    }

    displayExpanderPreview(previewData) {
        const table = document.getElementById('expand-results-table');
        if (!table) return;

        // 清空表格
        table.innerHTML = '';

        // 创建表头
        const thead = document.createElement('thead');
        thead.className = 'bg-gray-50';
        const headerRow = document.createElement('tr');
        
        const headers = ['推广计划', '推广组', '关键词', '匹配模式', '出价', '最终URL'];
        headers.forEach(header => {
            const th = document.createElement('th');
            th.className = 'px-4 py-2 text-left text-sm font-medium text-gray-700';
            th.textContent = header;
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        table.appendChild(thead);

        // 创建表体
        const tbody = document.createElement('tbody');
        tbody.className = 'divide-y divide-gray-200';
        
        previewData.forEach(row => {
            const tr = document.createElement('tr');
            tr.className = 'hover:bg-gray-50';
            tr.innerHTML = `
                <td class="px-4 py-2 text-sm text-gray-900">${row.Campaign || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-900">${row['Ad Group'] || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-900">${row.Keyword || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-900">${row['Match Type'] || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-900">${row.Bid || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-500 truncate max-w-xs">${row['Final URL'] || ''}</td>
            `;
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
    }

    async exportExpanderResult() {
        // 检查是否有任务ID
        if (!this.expanderCurrentTaskId) {
            this.showExpanderError('没有可导出的结果，请先完成关键词扩展');
            return;
        }

        try {
            const response = await fetch(`/api/export-expander-result/${this.expanderCurrentTaskId}`, {
                method: 'POST'
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `导出失败: ${response.status}`);
            }

            const blob = await response.blob();
            
            // 检查是否为有效的Excel文件
            if (blob.size === 0) {
                throw new Error('导出的文件为空');
            }

            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `expanded_keywords_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            this.showNotification('🎉 扩展结果导出成功！Excel文件已下载到您的设备', 'success');
            
            // 显示导出成功提示
            const exportBtn = document.getElementById('expand-export-button');
            if (exportBtn) {
                const successDiv = document.createElement('div');
                successDiv.className = 'export-success';
                successDiv.innerHTML = `
                    <i class="fas fa-check-circle mr-2"></i>
                    导出成功！扩展关键词文件已下载到您的设备
                `;
                exportBtn.parentNode.appendChild(successDiv);
                
                // 3秒后移除提示
                setTimeout(() => {
                    successDiv.remove();
                }, 3000);
            }
        } catch (error) {
            console.error('导出扩展结果失败:', error);
            this.showExpanderError(`导出失败: ${error.message}`);
        }
    }

    showExpanderResults() {
        const resultsSection = document.getElementById('expand-results-section');
        if (resultsSection) {
            resultsSection.classList.remove('hidden');
            resultsSection.classList.add('results-container');
            
            // 高亮导出按钮
            setTimeout(() => {
                const exportBtn = document.getElementById('expand-export-button');
                if (exportBtn) {
                    exportBtn.classList.add('export-button-highlight');
                    
                    // 5秒后停止闪烁
                    setTimeout(() => {
                        exportBtn.classList.remove('export-button-highlight');
                    }, 5000);
                }
            }, 1000);
        }
    }

    hideExpanderResults() {
        const resultsSection = document.getElementById('expand-results-section');
        if (resultsSection) {
            resultsSection.classList.add('hidden');
        }
    }

    showExpanderError(message) {
        const errorSection = document.getElementById('expand-error-section');
        const errorMessage = document.getElementById('expand-error-message');
        
        if (errorSection && errorMessage) {
            errorMessage.textContent = message;
            errorSection.classList.remove('hidden');
        }
    }

    hideExpanderError() {
        const errorSection = document.getElementById('expand-error-section');
        if (errorSection) {
            errorSection.classList.add('hidden');
        }
    }

    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，开始初始化...');

    try {
        window.keywordGrouper = new KeywordGrouper();
        console.log('KeywordGrouper初始化成功');
    } catch (error) {
        console.error('KeywordGrouper初始化失败:', error);
    }

    // 页面性能监控
    if (window.performance) {
        window.addEventListener('load', () => {
            const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
            console.log(`页面加载时间: ${loadTime}ms`);
        });
    }
});

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('JavaScript错误:', event.error);
    if (window.keywordGrouper) {
        window.keywordGrouper.showNotification('发生未知错误，请刷新页面重试', 'error');
    }
});

// 网络状态监控
window.addEventListener('online', () => {
    if (window.keywordGrouper) {
        window.keywordGrouper.showNotification('网络连接已恢复', 'success');
    }
});

window.addEventListener('offline', () => {
    if (window.keywordGrouper) {
        window.keywordGrouper.showNotification('网络连接已断开', 'error');
    }
}); 