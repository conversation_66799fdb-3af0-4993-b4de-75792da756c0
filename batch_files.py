#!/usr/bin/python
# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import os
from urllib.parse import parse_qs, urlparse

class FileReader:
    def __init__(self):
        self.encoding_fallback = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin-1']

    def read_file_with_encoding(self, file_path: str):
        """
        读取文件，支持多种格式和编码自动检测
        """
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return None
        
        # Excel文件处理
        if file_path.lower().endswith(('.xlsx', '.xls')):
            try:
                # 使用engine参数提高兼容性
                engine = 'openpyxl' if file_path.endswith('.xlsx') else 'xlrd'
                df = pd.read_excel(file_path, engine=engine)
                print(f"成功读取Excel文件: {file_path} ({len(df)} 行)")
                return df
            except Exception as e:
                print(f"读取Excel文件失败: {e}")
                return None
        
        for encoding in self.encoding_fallback:
            try:
                print(f"🔍 尝试 {encoding} 编码...")
                df = pd.read_csv(file_path, encoding=encoding, low_memory=False)
                # 验证数据质量
                if df.empty:
                    print(f"{encoding} 编码读取成功但数据为空")
                    continue  
                print(f"成功使用 {encoding} 编码读取 ({len(df)} 行, {len(df.columns)} 列)")
                return df
            except UnicodeDecodeError:
                continue
            except pd.errors.EmptyDataError:
                print(f"文件为空: {file_path}")
                return None
            except Exception as e:
                print(f"{encoding} 编码失败: {str(e)[:100]}")
                continue
        
        print("所有编码尝试失败")
        return None

    def process_dataframe(self, df):
        original_count = len(df)
        # 去除重复行
        df = df.drop_duplicates()
        # 去除全空行
        df = df.dropna(how='all')
        # 重置索引
        df = df.reset_index(drop=True)
        
        # 检查必要的列是否存在
        required_columns = ['关键词名称', '推广计划名称', '推广单元名称', '匹配模式', '出价']
        
        # 优化的列名映射 - 移除重复项，按优先级排序
        column_mapping = {
            '关键词名称': ['关键词', 'keyword', 'Keyword', 'KEYWORD'],
            '推广计划名称': ['推广计划', 'campaign', 'Campaign', 'CAMPAIGN', '计划名称'],
            '推广单元名称': ['推广单元', '推广组', 'ad group', 'Ad Group', 'AD GROUP', '单元名称'],
            '匹配模式': ['匹配方式', 'match type', 'Match Type', 'MATCH TYPE', '匹配类型','模式'],
            '出价': ['bid', 'Bid', 'BID', '价格', '出价金额', '关键词出价']
        }
        
        # 智能列名匹配
        matched_columns = {}
        for standard_col, alternatives in column_mapping.items():
            if standard_col in df.columns:
                matched_columns[standard_col] = standard_col
                continue
                
            # 查找替代列名
            for alt_col in alternatives:
                if alt_col in df.columns:
                    matched_columns[standard_col] = alt_col
                    df = df.rename(columns={alt_col: standard_col})
                    print(f"✅ 列名映射: '{alt_col}' -> '{standard_col}'")
                    break
        
        # 检查缺失的必要列
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"⚠️ 缺少必要的列: {missing_columns}")
            print(f"📋 当前文件包含的列: {list(df.columns)}")
            print(f"💡 请确保文件包含以下列名之一:")
            for missing_col in missing_columns:
                alternatives = column_mapping.get(missing_col, [])
                print(f"   {missing_col}: {alternatives}")
        else:
            print(f"✅ 所有必要列都已找到")
        
        cleaned_count = len(df)
        print(f"📊 数据清理完成: {original_count} -> {cleaned_count} 行")
        
        return df

    def parse_url(self, url: str, type="baidu") -> str:
        """
        解析并转换URL - 修改域名和参数
        """
        try:
            if pd.isna(url) or not url or str(url).strip() == '':
                return ""
            parsed_url = urlparse(str(url))
            query_params = parse_qs(parsed_url.query)
            keyID = query_params.get('keyID', [''])[0]

            types = {
                "baidu": 166,
                "sogou": 186,
                "bing": 196,
                "360": 216,
            }
            # 获取typeId，如果type不在字典中则使用baidu的默认值
            typeId = types.get(type, 166)

            # 修改域名为 https://sem.duba.net，sfrom=typeId，TFT=16
            print(f'https://sem.duba.net{parsed_url.path}?sfrom={typeId}&TFT=16&keyID={keyID}')
            return f'https://sem.duba.net{parsed_url.path}?sfrom={typeId}&TFT=16&keyID={keyID}'
        except Exception as e:
            print(f"URL解析失败: {url}, 错误: {e}")
            return str(url) if url else ""

    def process_keywords(self, df,type="bing"):
        print(df.columns)

    def convert_file(self, input_file_path: str,type:str="bing") -> bool:
        try:
            # 读取文件
            df = self.read_file_with_encoding(input_file_path)
            if df is None:
                return False
            
            # 处理数据
            df = self.process_dataframe(df)
            # 处理关键词
            df['计算机端落地页链接'] = df['计算机端落地页链接'].apply(lambda x: self.parse_url(x,type))

            print(df.head())
            print('数据清理完成')
            
        except Exception as e:
            print(f"转换过程中发生错误: {e}")
            return False



if __name__ == '__main__':
    file_path = os.path.join(os.getcwd(), "工作簿1.xlsx")
    
    print(f"📁 读取文件: {file_path}")
    file = FileReader()
    df = file.convert_file(file_path)
 
