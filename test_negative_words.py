#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试否定词功能
"""

from keyword_grouper import KeywordGrouper

def test_negative_words():
    """测试否定词过滤功能"""
    
    # 创建分组器
    grouper = KeywordGrouper()
    
    # 设置否定词
    negative_words = {'破解', '手机', '招聘', '论坛'}
    grouper.negative_words = negative_words
    
    print(f"设置的否定词: {negative_words}")
    
    # 测试关键词
    test_keywords = [
        'potplayer',
        'potplayer下载',
        'potplayer播放器',
        'potplayer官网',
        'potplayer安装',
        'potplayer中文版',
        'potplayer免费下载',
        'potplayer最新版',
        'potplayer绿色版',
        'potplayer破解版',  # 包含"破解"，应该被过滤
        'potplayer怎么用',
        'potplayer设置',
        'potplayer字幕',
        'potplayer快捷键',
        'potplayer皮肤',
        'potplayer手机版',  # 包含"手机"，应该被过滤
        'potplayer招聘',    # 包含"招聘"，应该被过滤
        'potplayer论坛'     # 包含"论坛"，应该被过滤
    ]
    
    print(f"\n原始关键词数量: {len(test_keywords)}")
    print("原始关键词:")
    for i, kw in enumerate(test_keywords, 1):
        print(f"  {i:2d}. {kw}")
    
    # 执行过滤
    filtered_keywords, removed_keywords = grouper.filter_keywords(test_keywords)
    
    print(f"\n过滤后关键词数量: {len(filtered_keywords)}")
    print("保留的关键词:")
    for i, kw in enumerate(filtered_keywords, 1):
        print(f"  {i:2d}. {kw}")
    
    print(f"\n被过滤的关键词数量: {len(removed_keywords)}")
    print("被过滤的关键词:")
    for i, kw in enumerate(removed_keywords, 1):
        print(f"  {i:2d}. {kw}")
    
    # 验证结果
    expected_removed = ['potplayer破解版', 'potplayer手机版', 'potplayer招聘', 'potplayer论坛']
    
    print(f"\n预期被过滤: {expected_removed}")
    print(f"实际被过滤: {removed_keywords}")
    
    if set(removed_keywords) == set(expected_removed):
        print("\n✅ 否定词过滤功能正常！")
        return True
    else:
        print("\n❌ 否定词过滤功能异常！")
        missing = set(expected_removed) - set(removed_keywords)
        extra = set(removed_keywords) - set(expected_removed)
        if missing:
            print(f"   应该被过滤但没有被过滤: {missing}")
        if extra:
            print(f"   不应该被过滤但被过滤了: {extra}")
        return False

if __name__ == "__main__":
    test_negative_words()
