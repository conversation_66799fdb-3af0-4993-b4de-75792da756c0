# -*- coding: utf-8 -*-
"""
关键词扩展器
根据基础关键词和模板文件生成扩展关键词
"""

import os
import pandas as pd
from tqdm import tqdm
from typing import List, Dict, Any, Set
import tempfile


class KeywordExpander:
    """关键词扩展器"""
    
    def __init__(self):
        self.codigo_file = 'codigo.xlsx'
        self.template_file = '1.1-关键词上传模版.xlsx'
    
    def keywords_list(self, keyword: str, file_path: str = None) -> List[str]:
        """
        根据关键词和模板文件生成关键词列表
        
        Args:
            keyword: 基础关键词
            file_path: 模板文件路径，默认使用codigo.xlsx
            
        Returns:
            生成的关键词列表
        """
        try:
            if file_path is None:
                file_path = self.codigo_file
                
            if not os.path.exists(file_path):
                return [keyword]  # 如果模板不存在，返回原关键词
            
            df = pd.read_excel(file_path)
            
            # 检查是否有'keys'列
            if 'keys' not in df.columns:
                return [keyword]
            
            # 替换模板中的"关键词"为实际关键词
            df['keys'] = df['keys'].astype(str).replace(to_replace=r'关键词', value=keyword, regex=True)
            
            # 返回非空的关键词列表
            keywords = df['keys'].dropna().tolist()
            return [kw for kw in keywords if str(kw).strip() != '' and str(kw) != 'nan']
            
        except Exception:
            return [keyword]
    
    def process_keywords(self, config: Dict[str, Any]) -> List[str]:
        """
        处理关键词列表，生成扩展关键词
        
        Args:
            config: 配置字典
            
        Returns:
            生成的唯一关键词列表
        """
        keywords_set: Set[str] = set()
        base_keywords = config.get('keywords', [])
        
        if not base_keywords:
            return []
        
        # 检查是否在Streamlit环境中
        try:
            import streamlit as st
            in_streamlit = True
        except:
            in_streamlit = False
        
        if in_streamlit:
            # 在Streamlit中不使用tqdm，使用简单的循环
            for keyword in base_keywords:
                if not keyword or str(keyword).strip() == '':
                    continue
                    
                new_keywords = set(self.keywords_list(keyword, config.get('codigo_file')))
                keywords_set.update(new_keywords)
        else:
            # 在命令行环境中使用tqdm
            with tqdm(base_keywords, desc="正在生成关键词", unit="关键词", ncols=80) as pbar:
                for keyword in pbar:
                    if not keyword or str(keyword).strip() == '':
                        continue
                        
                    new_keywords = set(self.keywords_list(keyword, config.get('codigo_file')))
                    keywords_set.update(new_keywords)
                    pbar.set_postfix({"已添加": len(new_keywords), "总数": len(keywords_set)})
        
        return list(keywords_set)
    
    def create_dataframe(self, keywords: List[str], config: Dict[str, Any]) -> pd.DataFrame:
        """
        创建关键词DataFrame
        
        Args:
            keywords: 关键词列表
            config: 配置字典
            
        Returns:
            包含关键词数据的DataFrame
        """
        if not keywords:
            return pd.DataFrame()
        
        return pd.DataFrame({
            'Keyword': keywords,
            'Campaign': config.get('campaign', ''),
            'Ad Group': config.get('ad_group', ''),
            'Match Type': config.get('match_type', 'Broad'),
            'Bid': config.get('bid', '0.00'),
            'Final Url': config.get('url', '')
        })
    
    def save_to_excel(self, df: pd.DataFrame, output_file: str) -> tuple:
        """
        保存DataFrame到Excel文件，按模板格式
        
        Args:
            df: 要保存的DataFrame
            output_file: 输出文件路径
            
        Returns:
            (保存是否成功, 实际输出文件路径)
        """
        try:
            if df.empty:
                return False, ""
            
            # 生成新的输出文件名，避免覆盖模板
            base_name, ext = os.path.splitext(output_file)
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            new_output_file = f"{base_name}_{timestamp}{ext}"
            
            # 如果模板文件存在，按模板列顺序排列
            if os.path.exists(output_file):
                template_df = pd.read_excel(output_file)
                template_columns = template_df.columns.tolist()
                
                # 确保所有模板列都存在
                for col in template_columns:
                    if col not in df.columns:
                        df[col] = ""
                
                # 重新排列列顺序
                df = df.reindex(columns=template_columns)
            
            # 保存到新文件
            df.to_excel(new_output_file, index=False)
            return True, new_output_file
            
        except Exception:
            return False, ""
    
    def expand_keywords(self, config: Dict[str, Any]) -> tuple:
        """
        执行关键词扩展的完整流程
        
        Args:
            config: 配置字典
            
        Returns:
            (成功标志, 生成的关键词数量, 输出文件路径)
        """
        try:
            # 1. 生成关键词
            keywords = self.process_keywords(config)
            if not keywords:
                return False, 0, ""
            
            # 2. 创建DataFrame
            df = self.create_dataframe(keywords, config)
            if df.empty:
                return False, 0, ""
            
            # 3. 确定输出文件路径
            output_file = config.get('output_file', self.template_file)
            if not os.path.isabs(output_file):
                output_file = os.path.join(os.getcwd(), output_file)
            
            # 4. 保存文件
            success, actual_output_file = self.save_to_excel(df, output_file)
            
            if success:
                return True, len(keywords), actual_output_file
            else:
                return False, 0, ""
                
        except Exception:
            return False, 0, ""
    
    def expand_from_upload(self, uploaded_file, config: Dict[str, Any]) -> tuple:
        """
        从上传的模板文件扩展关键词
        
        Args:
            uploaded_file: 上传的模板文件
            config: 配置字典
            
        Returns:
            (成功标志, 生成的关键词数量, DataFrame)
        """
        try:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_template_path = os.path.join(temp_dir, f"temp_template_{uploaded_file.name}")
            
            # 保存上传文件
            with open(temp_template_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # 更新配置中的模板文件路径
            config_copy = config.copy()
            config_copy['codigo_file'] = temp_template_path
            
            # 执行关键词扩展
            keywords = self.process_keywords(config_copy)
            if not keywords:
                return False, 0, None
            
            # 创建DataFrame
            df = self.create_dataframe(keywords, config_copy)
            
            # 清理临时文件
            if os.path.exists(temp_template_path):
                os.remove(temp_template_path)
            
            if not df.empty:
                return True, len(keywords), df
            else:
                return False, 0, None
                
        except Exception:
            return False, 0, None


def main():
    """命令行使用示例"""
    expander = KeywordExpander()
    
    # 默认配置
    config = {
        "match_type": "Exact",
        "bid": "0.39",
        "codigo_file": 'codigo.xlsx',
        "output_file": '1.1-关键词上传模版.xlsx',
        "campaign": "第三方软件-potplayer",
        "ad_group": "错拼词",
        "url": "https://sem.duba.net/sem/dseek/f255.html?sfrom=196&TFT=16&keyID=144553",
        "keywords": ['potplayer', "potplayer播放器", "potplayer下载"]
    }
    
    # 执行关键词扩展
    success, count, output_file = expander.expand_keywords(config)
    
    if success:
        print(f"关键词扩展成功完成！生成了 {count} 个关键词，保存到 {output_file}")
    else:
        print("关键词扩展失败！")


if __name__ == '__main__':
    main() 