<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义关键词生成工具</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-magic text-purple-600 text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold text-gray-900">自定义关键词生成工具</h1>
                    <span class="ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">V1.0</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-arrow-left mr-1"></i>返回主页
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- 功能介绍 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">🎯 自定义关键词生成</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    配置您的软件信息，智能生成大量高质量的长尾关键词，适用于SEM投放和SEO优化。
                    支持自定义软件名称和别名，快速生成符合您需求的关键词组合。
                </p>
            </div>
        </div>

        <!-- 配置表单 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 左侧：基础配置 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-cog text-purple-600 mr-2"></i>基础配置
                </h3>
                
                <form id="keywords-form">
                    <div class="space-y-4">
                        <!-- 软件主名 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                软件主名 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="main-name" 
                                   name="main_name" 
                                   placeholder="例如：微信"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                                   required>
                            <p class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-info-circle mr-1"></i>
                                这将用于生成文件名，修改时会自动更新下方的别名
                            </p>
                        </div>

                        <!-- 软件别名 -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <label class="block text-sm font-medium text-gray-700">
                                    软件别名/核心词/<span class="text-red-500 font-bold">手动加拼词/别名</span> <span class="text-red-500">*</span>
                                </label>
                                <button type="button" 
                                        id="clear-aliases-btn"
                                        class="text-xs text-purple-600 hover:text-purple-800 font-medium">
                                    <i class="fas fa-refresh mr-1"></i>
                                    自动生成
                                </button>
                            </div>
                            <textarea id="aliases" 
                                      name="aliases" 
                                      rows="6"
                                      placeholder="每行一个别名，例如：&#10;微信&#10;WeChat&#10;微信电脑版&#10;微信PC版&#10;微信桌面版"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                                      required></textarea>
                            <p class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-lightbulb mr-1"></i>
                                输入用户可能搜索的所有名称，每行一个。点击"自动生成"可根据主名生成默认别名。
                            </p>
                        </div>



                    </div>
                </form>
            </div>

            <!-- 右侧：高级选项 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-sliders-h text-purple-600 mr-2"></i>高级选项
                </h3>
                
                <div class="space-y-4">
                    <p class="text-sm text-gray-600 mb-4">选择要包含的关键词类型：</p>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" id="enable-download-intent" class="form-checkbox h-4 w-4 text-purple-600 rounded focus:ring-purple-500" checked>
                            <span class="ml-2 text-sm text-gray-700">下载意图词</span>
                            <span class="ml-2 text-xs text-gray-500">（下载、安装、安装包等）</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="enable-version-intent" class="form-checkbox h-4 w-4 text-purple-600 rounded focus:ring-purple-500" checked>
                            <span class="ml-2 text-sm text-gray-700">版本意图词</span>
                            <span class="ml-2 text-xs text-gray-500">（最新版、2024版、稳定版等）</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="enable-tech-spec-intent" class="form-checkbox h-4 w-4 text-purple-600 rounded focus:ring-purple-500" checked>
                            <span class="ml-2 text-sm text-gray-700">技术规格词</span>
                            <span class="ml-2 text-xs text-gray-500">（64位、Windows、PC专用等）</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="enable-risk-value-intent" class="form-checkbox h-4 w-4 text-purple-600 rounded focus:ring-purple-500" checked>
                            <span class="ml-2 text-sm text-gray-700">价值意图词</span>
                            <span class="ml-2 text-xs text-gray-500">（绿色版、免安装版、纯净版等）</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="enable-problem-intent" class="form-checkbox h-4 w-4 text-purple-600 rounded focus:ring-purple-500" checked>
                            <span class="ml-2 text-sm text-gray-700">问题意图词</span>
                            <span class="ml-2 text-xs text-gray-500">（怎么用、如何安装等）</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-6">
            <div class="flex justify-center">
                <button id="generate-btn" 
                        class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-magic mr-2"></i>
                    生成关键词
                </button>
            </div>
        </div>

        <!-- 进度显示 -->
        <div id="progress-section" class="hidden bg-white rounded-lg shadow-md p-6 mt-6">
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">正在生成关键词...</h3>
                <p id="progress-message" class="text-gray-600 mb-4">初始化中...</p>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div id="progress-bar" class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <div id="progress-details" class="text-sm text-gray-500"></div>
            </div>
        </div>

        <!-- 结果显示 -->
        <div id="results-section" class="hidden">
            <!-- 统计信息 -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-chart-bar text-green-600 mr-2"></i>生成统计
                </h3>
                <div id="statistics" class="grid grid-cols-2 md:grid-cols-4 gap-4"></div>
            </div>

            <!-- 关键词预览 -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-eye text-blue-600 mr-2"></i>关键词预览
                    </h3>
                    <button id="export-btn" 
                            class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 flex items-center">
                        <i class="fas fa-download mr-2"></i>
                        导出CSV
                    </button>
                </div>
                <div id="keywords-preview" class="max-h-64 overflow-y-auto border border-gray-200 rounded-lg p-4"></div>
            </div>
        </div>
    </main>

    <script>
        let currentTaskId = null;
        let pollInterval = null;

        // 生成关键词
        document.getElementById('generate-btn').addEventListener('click', async function() {
            console.log('=== 开始生成关键词 ===');
            
            const mainName = document.getElementById('main-name').value.trim();
            const aliases = document.getElementById('aliases').value.trim();
            
            console.log('输入数据:', {
                mainName: mainName,
                aliases: aliases,
                aliasesLength: aliases.length
            });
            
            if (!mainName || !aliases) {
                console.error('验证失败: 缺少必要字段');
                alert('请填写软件主名和至少一个别名');
                return;
            }

            const formData = new FormData();
            formData.append('main_name', mainName);
            formData.append('aliases', aliases);
            formData.append('features', '');
            formData.append('enable_download_intent', document.getElementById('enable-download-intent').checked);
            formData.append('enable_version_intent', document.getElementById('enable-version-intent').checked);
            formData.append('enable_tech_spec_intent', document.getElementById('enable-tech-spec-intent').checked);
            formData.append('enable_risk_value_intent', document.getElementById('enable-risk-value-intent').checked);
            formData.append('enable_problem_intent', document.getElementById('enable-problem-intent').checked);

            console.log('FormData内容:');
            for (let [key, value] of formData.entries()) {
                console.log(`  ${key}: ${value}`);
            }

            try {
                console.log('发送API请求到: /api/generate-custom-keywords');
                
                const response = await fetch('/api/generate-custom-keywords', {
                    method: 'POST',
                    body: formData
                });

                console.log('API响应状态:', {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API响应错误:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}\n响应内容: ${errorText}`);
                }

                const result = await response.json();
                console.log('API响应结果:', result);
                
                if (result.success) {
                    console.log('任务创建成功, Task ID:', result.task_id);
                    currentTaskId = result.task_id;
                    showProgress();
                    startPolling();
                } else {
                    console.error('API返回失败:', result.error);
                    alert('生成失败：' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('请求异常:', error);
                console.error('错误堆栈:', error.stack);
                alert('生成失败：' + error.message);
            }
        });

        // 显示进度
        function showProgress() {
            document.getElementById('progress-section').classList.remove('hidden');
            document.getElementById('results-section').classList.add('hidden');
            document.getElementById('generate-btn').disabled = true;
            document.getElementById('generate-btn').innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>生成中...';
        }

        // 开始轮询状态
        function startPolling() {
            console.log('开始轮询任务状态, Task ID:', currentTaskId);
            
            if (pollInterval) {
                clearInterval(pollInterval);
            }
            
            pollInterval = setInterval(async () => {
                try {
                    console.log(`轮询状态: /api/status/${currentTaskId}`);
                    const response = await fetch(`/api/status/${currentTaskId}`);
                    
                    console.log('状态查询响应:', {
                        status: response.status,
                        statusText: response.statusText,
                        ok: response.ok
                    });
                    
                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('状态查询失败:', errorText);
                        return;
                    }
                    
                    const status = await response.json();
                    console.log('状态查询结果:', status);
                    
                    if (status.success) {
                        updateProgress(status);
                        
                        if (status.status === 'completed') {
                            console.log('任务完成, 显示结果');
                            clearInterval(pollInterval);
                            showResults(status.result);
                        } else if (status.status === 'failed') {
                            console.log('任务失败:', status.error);
                            clearInterval(pollInterval);
                            showError(status.error);
                        } else {
                            console.log('任务进行中:', status.current_step, status.progress + '%');
                        }
                    } else {
                        console.error('状态查询返回失败:', status);
                    }
                } catch (error) {
                    console.error('轮询异常:', error);
                    console.error('错误堆栈:', error.stack);
                }
            }, 1000);
        }

        // 更新进度
        function updateProgress(status) {
            document.getElementById('progress-message').textContent = status.message;
            document.getElementById('progress-bar').style.width = status.progress + '%';
            document.getElementById('progress-details').textContent = `当前步骤: ${status.current_step}`;
        }

        // 显示结果
        function showResults(result) {
            document.getElementById('progress-section').classList.add('hidden');
            document.getElementById('results-section').classList.remove('hidden');
            
            // 重置按钮
            document.getElementById('generate-btn').disabled = false;
            document.getElementById('generate-btn').innerHTML = '<i class="fas fa-magic mr-2"></i>生成关键词';
            
            // 显示统计信息
            const statistics = result.statistics;
            document.getElementById('statistics').innerHTML = `
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">${statistics.aliases_count}</div>
                    <div class="text-sm text-gray-500">别名数量</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">${statistics.intent_words_count}</div>
                    <div class="text-sm text-gray-500">意图词数量</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">${statistics.generated_count}</div>
                    <div class="text-sm text-gray-500">生成数量</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">${result.total_count}</div>
                    <div class="text-sm text-gray-500">最终数量</div>
                </div>
            `;
            
            // 显示关键词预览
            const preview = result.preview.map(keyword => 
                `<div class="inline-block bg-gray-100 rounded-lg px-3 py-1 m-1 text-sm">${keyword}</div>`
            ).join('');
            
            document.getElementById('keywords-preview').innerHTML = preview + 
                `<div class="text-center mt-4 text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    显示前20个关键词，共${result.total_count}个
                </div>`;
        }

        // 显示错误
        function showError(error) {
            document.getElementById('progress-section').classList.add('hidden');
            document.getElementById('generate-btn').disabled = false;
            document.getElementById('generate-btn').innerHTML = '<i class="fas fa-magic mr-2"></i>生成关键词';
            alert('生成失败：' + error);
        }

        // 导出CSV
        document.getElementById('export-btn').addEventListener('click', async function() {
            if (!currentTaskId) {
                alert('请先生成关键词');
                return;
            }

            try {
                const response = await fetch(`/api/export-custom-keywords/${currentTaskId}`, {
                    method: 'POST'
                });

                const result = await response.json();
                
                if (result.success) {
                    window.location.href = result.download_url;
                } else {
                    alert('导出失败：' + result.error);
                }
            } catch (error) {
                console.error('Export error:', error);
                alert('导出失败：' + error.message);
            }
        });

        // 软件主名变化时自动更新别名
        let previousMainName = '';
        
        function updateAliasesWhenMainNameChanges() {
            const mainNameInput = document.getElementById('main-name');
            const aliasesInput = document.getElementById('aliases');
            
            mainNameInput.addEventListener('input', function() {
                const currentMainName = this.value.trim();
                
                // 如果主名为空或与之前相同，不做处理
                if (!currentMainName || currentMainName === previousMainName) {
                    return;
                }
                
                // 如果是第一次输入且不是默认值，不做处理
                if (!previousMainName && currentMainName !== '微信') {
                    previousMainName = currentMainName;
                    return;
                }
                
                // 如果有之前的主名，进行替换
                if (previousMainName) {
                    let currentAliases = aliasesInput.value;
                    
                    // 替换逻辑：将别名中包含旧主名的词替换为新主名
                    const aliasLines = currentAliases.split('\n');
                    const updatedAliases = aliasLines.map(line => {
                        line = line.trim();
                        if (!line) return line;
                        
                        // 如果整行就是旧主名，直接替换
                        if (line === previousMainName) {
                            return currentMainName;
                        }
                        
                        // 替换复合词中的主名部分
                        // 处理中文情况：如"微信电脑版" -> "QQ电脑版"
                        if (line.includes(previousMainName)) {
                            return line.replace(new RegExp(previousMainName, 'g'), currentMainName);
                        }
                        
                        // 处理英文情况：如果旧主名是中文，新主名是英文，需要特殊处理
                        // 这里可以根据需要扩展更复杂的逻辑
                        
                        return line;
                    });
                    
                    // 更新别名输入框
                    aliasesInput.value = updatedAliases.join('\n');
                }
                
                // 更新之前的主名
                previousMainName = currentMainName;
            });
        }
        
        // 智能生成默认别名模板
        function generateDefaultAliases(mainName) {
            if (!mainName) return '';
            
            const templates = [
                mainName,
                `${mainName}电脑版`,
                `${mainName}PC版`,
                `${mainName}桌面版`,
                `电脑版${mainName}`,
                `PC版${mainName}`,
                `桌面版${mainName}`,
                `${mainName}客户端`,
                `PC${mainName}`,
                `电脑${mainName}`
            ];
            
            return templates.join('\n');
        }
        
                 // 主名失去焦点时，如果别名为空，自动生成默认别名
        function setupAutoGenerateAliases() {
            const mainNameInput = document.getElementById('main-name');
            const aliasesInput = document.getElementById('aliases');
            
            mainNameInput.addEventListener('blur', function() {
                const mainName = this.value.trim();
                const currentAliases = aliasesInput.value.trim();
                
                // 如果主名不为空且别名为空，自动生成默认别名
                if (mainName && !currentAliases) {
                    aliasesInput.value = generateDefaultAliases(mainName);
                    previousMainName = mainName;
                }
            });
        }
        
        // 设置自动生成按钮点击事件
        function setupAutoGenerateButton() {
            const clearBtn = document.getElementById('clear-aliases-btn');
            const mainNameInput = document.getElementById('main-name');
            const aliasesInput = document.getElementById('aliases');
            
            clearBtn.addEventListener('click', function() {
                const mainName = mainNameInput.value.trim();
                
                if (!mainName) {
                    alert('请先输入软件主名');
                    mainNameInput.focus();
                    return;
                }
                
                // 生成默认别名
                aliasesInput.value = generateDefaultAliases(mainName);
                previousMainName = mainName;
                
                // 添加视觉反馈
                clearBtn.innerHTML = '<i class="fas fa-check mr-1"></i>已生成';
                clearBtn.classList.add('text-green-600');
                clearBtn.classList.remove('text-purple-600');
                
                setTimeout(() => {
                    clearBtn.innerHTML = '<i class="fas fa-refresh mr-1"></i>自动生成';
                    clearBtn.classList.remove('text-green-600');
                    clearBtn.classList.add('text-purple-600');
                }, 2000);
            });
        }

        // 页面加载时设置默认值
        window.onload = function() {
            document.getElementById('main-name').value = '微信';
            document.getElementById('aliases').value = '微信\n微信电脑版\n微信PC版\n微信桌面版\n电脑版微信\nPC版微信\n桌面版微信\n微信客户端\nPC微信\n电脑微信';
            
            // 初始化功能
            previousMainName = '微信';
            updateAliasesWhenMainNameChanges();
            setupAutoGenerateAliases();
            setupAutoGenerateButton();
        };
    </script>
</body>
</html> 