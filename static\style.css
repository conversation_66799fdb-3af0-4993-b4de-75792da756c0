/* 现代化的CSS样式 - SEM关键词智能分组工具 V2.0 优化版 */

/* CSS 变量定义 */
:root {
    /* 主色调 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --primary-light: #dbeafe;
    --primary-dark: #1e40af;
    
    /* 次要色调 */
    --secondary-color: #10b981;
    --secondary-hover: #059669;
    --secondary-light: #d1fae5;
    
    /* 警告色 */
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --warning-light: #fef3c7;
    
    /* 危险色 */
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --danger-light: #fecaca;
    
    /* 中性色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;
    
    /* 间距 */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
    
    /* 过渡效果 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 基础重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* 基础样式 */
body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 导航栏增强 */
nav {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1000;
}

nav h1 {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* 主要内容区域 */
main {
    background: transparent;
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
}

/* 卡片样式增强 */
.card-hover,
.bg-white.rounded-lg {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    border-radius: var(--radius-lg);
}

.card-hover:hover,
.bg-white.rounded-lg:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 按钮系统重构 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 14px;
    line-height: 1.5;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 44px;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.btn:active {
    transform: translateY(1px);
}

/* 主要按钮 */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 成功按钮 */
.btn-success {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--secondary-hover), #047857);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 警告按钮 */
.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-hover));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-hover), #b45309);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 禁用状态 */
.btn:disabled {
    background: var(--gray-300);
    color: var(--gray-500);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.6;
}

/* 文件上传区域 */
#upload-area,
#baidu-upload-area,
#expand-template-upload-area,
#expand-keywords-upload-area {
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

#upload-area:hover,
#baidu-upload-area:hover,
#expand-template-upload-area:hover,
#expand-keywords-upload-area:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-light), rgba(255, 255, 255, 0.9));
    transform: scale(1.01);
}

#upload-area.dragover,
#baidu-upload-area.dragover,
#expand-template-upload-area.dragover,
#expand-keywords-upload-area.dragover {
    border-color: var(--primary-hover);
    background: linear-gradient(135deg, var(--primary-light), #bfdbfe);
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* 进度条系统 */
.progress-container {
    position: relative;
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.progress-bar-primary {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.progress-bar-warning {
    background: linear-gradient(90deg, var(--warning-color), var(--warning-hover));
}

.progress-bar-success {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-hover));
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: translateX(-100%);
    animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 表单控件增强 */
.form-control {
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 14px;
    line-height: 1.5;
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
}

.form-control::placeholder {
    color: var(--gray-400);
}

/* 复选框增强 */
.form-checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    background: white;
}

.form-checkbox:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.form-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 标签页样式增强 */
.tab-button {
    padding: var(--spacing-md) var(--spacing-xl);
    background: transparent;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: var(--gray-600);
    transition: all var(--transition-normal);
    border-bottom: 3px solid transparent;
    position: relative;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.tab-button::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.tab-button:hover {
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.tab-button.active {
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
}

.tab-button.active::before {
    transform: scaleX(1);
}

/* 统计卡片增强 */
.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 分组卡片增强 */
.group-card {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
}

.group-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.group-header {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
}

.group-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
}

.group-header:hover {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
}

.group-content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.group-content.expanded {
    max-height: 500px;
    padding: var(--spacing-lg);
    }
    
    .group-keywords {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xs);
    max-height: 350px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--gray-300) transparent;
}

.keyword-item {
    background: var(--gray-50);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
    font-size: 14px;
    cursor: pointer;
}

.keyword-item:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* 动画增强 */
@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes slideIn {
    from { 
        transform: translateX(-100%); 
        opacity: 0; 
    }
    to { 
        transform: translateX(0); 
        opacity: 1; 
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { 
        transform: translate3d(0, 0, 0); 
    }
    40%, 43% { 
        transform: translate3d(0, -8px, 0); 
    }
    70% { 
        transform: translate3d(0, -4px, 0); 
    }
    90% { 
        transform: translate3d(0, -2px, 0); 
    }
}

@keyframes pulse {
    0% { 
        transform: scale(1); 
    }
    50% { 
        transform: scale(1.05); 
    }
    100% { 
        transform: scale(1); 
    }
}

@keyframes shake {
    0%, 100% { 
        transform: translateX(0); 
    }
    25% { 
        transform: translateX(-5px); 
    }
    75% { 
        transform: translateX(5px); 
    }
}

/* 动画类 */
.animate-fadeIn {
    animation: fadeIn 0.6s ease-out;
}

.animate-slideIn {
    animation: slideIn 0.5s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

/* 加载状态 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transform: translateX(-100%);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 工具提示 */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 12px;
    pointer-events: none;
    transform: translate(-50%, -100%);
    margin-top: -8px;
    opacity: 0;
    transition: opacity var(--transition-normal);
    backdrop-filter: blur(10px);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

.tooltip.show {
    opacity: 1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-sm);
    transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    main {
        padding: var(--spacing-md);
    }
    
    .grid-cols-4 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    :root {
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
    }
    
    main {
        padding: var(--spacing-sm);
    }
    
    .grid-cols-4,
    .grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .group-keywords {
        grid-template-columns: 1fr;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 13px;
    }
    
    .tab-button {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 14px;
    }
}

@media (max-width: 640px) {
    .grid-cols-2 {
        grid-template-columns: 1fr;
    }
    
    .stat-number {
        font-size: 1.75rem;
    }
    
    .group-header {
        padding: var(--spacing-md);
    }
    
    .group-content.expanded {
        padding: var(--spacing-md);
    }
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
    :root {
        --gray-300: #000;
        --gray-600: #000;
        --primary-color: #0000ff;
        --secondary-color: #008000;
    }
    
    .form-control {
        border: 2px solid #000;
    }
    
    .btn-primary {
        background: #000;
        color: #fff;
    }
    
    .group-card {
        border: 2px solid #000;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
        --gray-400: #9ca3af;
        --gray-500: #d1d5db;
        --gray-600: #e5e7eb;
        --gray-700: #f3f4f6;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
    
    body {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        color: var(--gray-800);
    }
    
    .bg-white {
        background: rgba(31, 41, 55, 0.95) !important;
        color: var(--gray-800);
    }
    
    nav {
        background: rgba(31, 41, 55, 0.95);
    }
    
    .form-control {
        background: rgba(75, 85, 99, 0.8);
        border-color: var(--gray-300);
        color: var(--gray-800);
    }
    
    .keyword-item {
        background: var(--gray-200);
        border-color: var(--gray-300);
        color: var(--gray-700);
    }
    
    .group-header {
        background: linear-gradient(135deg, var(--gray-200), var(--gray-100));
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
        color: black;
    }
    
    .no-print {
        display: none !important;
    }
    
    .group-content {
        max-height: none !important;
        padding: var(--spacing-lg) !important;
    }
    
    .btn {
        border: 1px solid #000;
        background: white;
        color: black;
    }
}

/* 特定功能样式 */
.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

.hidden {
    display: none !important;
}

/* 导出按钮特殊样式 */
#export-complete-btn,
#baidu-export-btn,
#expand-export-button {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover)) !important;
    border: none !important;
    color: white !important;
    padding: var(--spacing-md) var(--spacing-xl) !important;
    border-radius: var(--radius-md) !important;
    cursor: pointer !important;
    transition: all var(--transition-normal) !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    box-shadow: var(--shadow-lg) !important;
    position: relative !important;
    overflow: hidden !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 48px !important;
    text-decoration: none !important;
    gap: var(--spacing-xs) !important;
}

#export-complete-btn:hover,
#baidu-export-btn:hover,
#expand-export-button:hover {
    background: linear-gradient(135deg, var(--secondary-hover), #047857) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-xl) !important;
}

#export-complete-btn:active,
#baidu-export-btn:active,
#expand-export-button:active {
    transform: translateY(0) !important;
    box-shadow: var(--shadow-md) !important;
}

/* 开始按钮样式 */
#process-btn,
#baidu-convert-btn,
#expand-start-button {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover)) !important;
    border: none !important;
    color: white !important;
    padding: var(--spacing-md) var(--spacing-xl) !important;
    border-radius: var(--radius-md) !important;
    cursor: pointer !important;
    transition: all var(--transition-normal) !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    box-shadow: var(--shadow-lg) !important;
    position: relative !important;
    overflow: hidden !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 48px !important;
    text-decoration: none !important;
    gap: var(--spacing-xs) !important;
}

#process-btn:hover,
#baidu-convert-btn:hover,
#expand-start-button:hover {
    background: linear-gradient(135deg, var(--secondary-hover), #047857) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-xl) !important;
}

#process-btn:active,
#baidu-convert-btn:active,
#expand-start-button:active {
    transform: translateY(0) !important;
    box-shadow: var(--shadow-md) !important;
}

#expand-start-button:disabled {
    background: linear-gradient(135deg, var(--gray-300), var(--gray-400)) !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6 !important;
}

/* 进度条特定样式 */
#progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover)) !important;
}

#baidu-progress-bar {
    background: linear-gradient(90deg, var(--warning-color), var(--warning-hover)) !important;
}

#expand-progress-bar {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-hover)) !important;
}

/* 配置摘要增强 */
#expand-config-summary {
    background: linear-gradient(135deg, var(--warning-light), #fed7aa) !important;
    border: 2px solid var(--warning-color) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--spacing-lg) !important;
    margin-bottom: var(--spacing-lg) !important;
    box-shadow: var(--shadow-md) !important;
    transition: all var(--transition-normal) !important;
}

#expand-config-summary:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-lg) !important;
}

#expand-summary-text {
    color: var(--warning-hover) !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* 错误提示增强 */
.error-container {
    background: linear-gradient(135deg, var(--danger-light), #fecaca) !important;
    border: 2px solid var(--danger-color) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--spacing-xl) !important;
    margin: var(--spacing-lg) 0 !important;
    box-shadow: var(--shadow-md) !important;
    animation: shake 0.5s ease-in-out !important;
}

/* 确保所有交互元素都有足够的点击区域 */
button,
.btn,
input[type="checkbox"],
input[type="radio"] {
    min-height: 20px;
    min-width: 20px;
}

/* 焦点样式统一 */
button:focus,
.btn:focus,
input:focus,
textarea:focus,
select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 性能优化 - 硬件加速 */
.card-hover,
.btn,
.group-card,
.tab-button,
.stat-card {
    will-change: transform;
    transform: translateZ(0);
}

/* 新增：关键词输入界面样式 */
.form-radio {
    appearance: none;
    background-color: #fff;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.form-radio:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-radio:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
}

.form-radio:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 关键词文本区域样式 */
#keywords-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.5;
    resize: vertical;
    min-height: 200px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

#keywords-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 关键词计数样式 */
#keywords-count {
    font-weight: 600;
    color: var(--primary-color);
}

/* 输入方式切换动画 */
#text-input-area,
#file-input-area {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

#text-input-area.hidden,
#file-input-area.hidden {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
}

/* 示例关键词按钮样式 */
#sample-keywords-btn {
    position: relative;
    overflow: hidden;
}

#sample-keywords-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

#sample-keywords-btn:hover::before {
    left: 100%;
}

/* 处理按钮增强样式 */
#process-text-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--secondary-color), #059669);
    border: none;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

#process-text-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--secondary-hover), #047857);
}

#process-text-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

#process-text-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

#process-text-btn:hover::before {
    left: 100%;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    #keywords-textarea {
        height: 150px;
        font-size: 14px;
    }

    .form-radio {
        width: 18px;
        height: 18px;
    }

    #process-text-btn {
        width: 100%;
        padding: 12px;
    }
}