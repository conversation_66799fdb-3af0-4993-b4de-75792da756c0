"""
SEM关键词智能分组工具 - Web UI版本 (V2.0)
基于Streamlit框架的用户友好界面，与keyword_grouper.py功能完全一致
"""

import streamlit as st
import pandas as pd
import io
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from keyword_grouper import KeywordGrouper
from keyword_id_syncer import KeywordIdSyncer
import os
import tempfile

@st.cache_data
def load_default_negative_words():
    """加载默认否定词文件（带缓存）"""
    try:
        negative_words_file = "keyword/negative_words.txt"
        if os.path.exists(negative_words_file):
            with open(negative_words_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                return content
        else:
            # 如果文件不存在，返回一些默认的否定词
            default_words = [
                "招聘", "论坛", "破解", "手机", "app", "安卓", "ios", "iphone", "android",
                "苹果", "移动", "电话", "游戏", "小说", "视频",
                "图片", "壁纸", "头像", "表情", "emoji", "聊天", "社交", "交友",
                "直播", "短视频", "抖音", "快手", "微博"
            ]
            return '\n'.join(default_words)
    except Exception as e:
        st.error(f"加载否定词文件失败: {e}")
        return ""

# 页面配置
st.set_page_config(
    page_title="SEM关键词智能分组工具 V2.0",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 添加CSS样式优化 - 简洁专业设计
st.markdown("""
<style>
    /* 全局样式重置和优化 */
    .main .block-container {
        padding-top: 1rem;
        padding-bottom: 1rem;
        max-width: 1200px;
    }
    
    /* 主标题样式 - 简洁版 */
    .main-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 500;
        text-align: center;
        margin-bottom: 0.3rem;
    }
    
    /* 副标题样式 - 简洁版 */
    .subtitle {
        font-size: 0.9rem;
        color: #6c757d;
        text-align: center;
        margin-bottom: 1rem;
        font-weight: 400;
    }
    
    /* 按钮样式优化 - 字小一点，高度低一些 */
    .stButton > button {
        background: #007bff;
        border: none;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        font-size: 0.85rem;
        padding: 0.5rem 1.2rem;
        height: auto;
        min-height: 2.2rem;
        transition: all 0.2s ease;
    }
    
    .stButton > button:hover {
        background: #0056b3;
    }
    
    /* 主要按钮样式 */
    .stButton > button[kind="primary"] {
        background: #28a745;
        font-size: 0.9rem;
        font-weight: 600;
        min-height: 2.5rem;
    }
    
    .stButton > button[kind="primary"]:hover {
        background: #218838;
    }
    
    /* 进度条样式 */
    .stProgress > div > div > div > div {
        background: #007bff;
        border-radius: 4px;
    }
    
    /* 信息框样式优化 - 字小一点，高度低一些 */
    .stInfo {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 0.6rem 0.8rem;
        margin: 0.4rem 0;
        font-size: 0.8rem;
    }
    
    .stSuccess {
        background: #f8f9fa;
        border: 1px solid #d4edda;
        border-left: 4px solid #28a745;
        border-radius: 6px;
        padding: 0.6rem 0.8rem;
        margin: 0.4rem 0;
        font-size: 0.8rem;
    }
    
    .stWarning {
        background: #f8f9fa;
        border: 1px solid #ffeaa7;
        border-left: 4px solid #ffc107;
        border-radius: 6px;
        padding: 0.6rem 0.8rem;
        margin: 0.4rem 0;
        font-size: 0.8rem;
    }
    
    .stError {
        background: #f8f9fa;
        border: 1px solid #f5c6cb;
        border-left: 4px solid #dc3545;
        border-radius: 6px;
        padding: 0.6rem 0.8rem;
        margin: 0.4rem 0;
        font-size: 0.8rem;
    }
    
    /* 标签页样式优化 - tab字大一点 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 1rem;
    }
    
    .stTabs [data-baseweb="tab"] {
        font-size: 1.1rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 8px 8px 0 0;
    }
    
    .stTabs [aria-selected="true"] {
        background: #007bff;
        color: white;
    }
    
    /* 标题样式优化 - 字小一点 */
    h1, h2, h3, h4 {
        color: #2c3e50;
        font-weight: 600;
        margin-top: 0.75rem;
        margin-bottom: 0.4rem;
    }
    
    h2 {
        font-size: 1.3rem;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 0.4rem;
    }
    
    h3 {
        font-size: 1.1rem;
        color: #495057;
    }
    
    h4 {
        font-size: 1rem;
        color: #6c757d;
    }
    
    /* 侧边栏样式优化 */
    .css-1d391kg {
        background: #f8f9fa;
        padding: 1rem;
        border-right: 1px solid #e9ecef;
    }
    
    /* 文本区域样式 - 字小一点，高度低一些 */
    .stTextArea textarea {
        font-size: 0.8rem;
        border-radius: 6px;
        border: 1px solid #ced4da;
        padding: 0.6rem;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        line-height: 1.4;
    }
    
    .stTextArea textarea:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
    }
    
    /* 文件上传区域优化 - 高度低一些 */
    .stFileUploader > section > button {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 2px dashed #007bff;
        border-radius: 12px;
        padding: 1.2rem;
        min-height: 4rem;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        font-weight: 500;
        color: #007bff;
        position: relative;
    }
    
    .stFileUploader > section > button:hover {
        border-color: #0056b3;
        background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.15);
    }
    
    .stFileUploader > section > button::before {
        content: "📁";
        font-size: 1.5rem;
        display: block;
        margin-bottom: 0.3rem;
    }
    
    /* 紧凑型文件上传器 - 高度低一些 */
    .compact-uploader .stFileUploader > section > button {
        background: #f8f9fa;
        border: 1px dashed #ced4da;
        border-radius: 6px;
        padding: 0.6rem;
        min-height: 2.5rem;
        font-size: 0.8rem;
        font-weight: 400;
        color: #6c757d;
    }
    
    .compact-uploader .stFileUploader > section > button:hover {
        border-color: #007bff;
        background: #f0f8ff;
        transform: none;
        box-shadow: none;
    }
    
    .compact-uploader .stFileUploader > section > button::before {
        content: "📄";
        font-size: 0.9rem;
        display: inline;
        margin-right: 0.2rem;
        margin-bottom: 0;
    }
    
    /* 扩展框样式优化 - 字小一点，高度低一些 */
    .streamlit-expanderHeader {
        background: #f8f9fa;
        border-radius: 6px;
        font-size: 0.85rem;
        font-weight: 500;
        padding: 0.6rem 0.8rem;
        border: 1px solid #e9ecef;
    }
    
    .streamlit-expanderContent {
        background: #ffffff;
        border-radius: 0 0 6px 6px;
        padding: 0.8rem;
        border: 1px solid #e9ecef;
        border-top: none;
    }
    
    /* 选择框和输入框样式 - 字小一点 */
    .stSelectbox, .stTextInput {
        margin-bottom: 0.6rem;
    }
    
    .stSelectbox > div > div {
        border-radius: 6px;
        border: 1px solid #ced4da;
        font-size: 0.85rem;
    }
    
    .stTextInput > div > div > input {
        border-radius: 6px;
        border: 1px solid #ced4da;
        padding: 0.45rem 0.6rem;
        font-size: 0.85rem;
    }
    
    /* 复选框和单选框样式 - 字小一点 */
    .stCheckbox, .stRadio {
        margin-bottom: 0.4rem;
        font-size: 0.85rem;
    }
    
    /* 简洁卡片样式 */
    .simple-card {
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    
    /* 内联提示样式 - 字小一点，高度低一些 */
    .inline-tip {
        background: #f8f9fa;
        border-left: 3px solid #007bff;
        padding: 0.4rem 0.6rem;
        margin: 0.4rem 0;
        font-size: 0.8rem;
        color: #495057;
        border-radius: 0 4px 4px 0;
    }
    
    /* 数据表格样式 */
    .dataframe {
        border-radius: 6px;
        overflow: hidden;
        border: 1px solid #e9ecef;
    }
    
    /* 紧凑布局 */
    .compact-row {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.5rem;
    }
    
    /* 状态指示器 - 简洁版 */
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        background: #e9ecef;
        border-radius: 4px;
        font-size: 0.8rem;
        color: #495057;
        margin-right: 0.5rem;
    }
    
    .status-badge.success {
        background: #d4edda;
        color: #155724;
    }
    
    .status-badge.info {
        background: #d1ecf1;
        color: #0c5460;
    }
    
</style>
""", unsafe_allow_html=True)

def main():
    """主应用函数"""
    
    # 简洁的标题设计
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 class="main-title">🔍 SEM关键词智能分组工具 V2.0</h1>
        <p class="subtitle">智能分组 · 精准识别 · 高效管理</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 紧凑型侧边栏配置
    with st.sidebar:
        st.markdown("### ⚙️ 配置选项")
        
        # 简洁的分组策略说明
        st.markdown("""
        <div class="inline-tip">
            📊 规则分组算法，专门针对电脑下载类软件优化
        </div>
        """, unsafe_allow_html=True)
        
        # 长度过滤设置为None（不使用）
        min_length, max_length = None, None
        
        # 快捷配置预设
        st.markdown("#### ⚡ 快捷配置")
        preset = st.selectbox(
            "选择预设配置",
            options=["自定义配置", "推荐配置（智能+n-gram）", "快速配置（传统算法）", "精准配置（仅智能算法）"],
            index=1,
            help="选择预设配置可以快速开始，也可以选择自定义配置进行详细设置"
        )
        
        # 根据预设设置默认值
        if preset == "推荐配置（智能+n-gram）":
            default_brand_algorithm = "智能词组识别"
            default_ngram = True
            default_show_filtered = True
            default_core_recommendation = True
            default_negative_option = "在默认基础上修改"
        elif preset == "快速配置（传统算法）":
            default_brand_algorithm = "传统单词识别"
            default_ngram = False
            default_show_filtered = False
            default_core_recommendation = True
            default_negative_option = "使用默认否定词"
        elif preset == "精准配置（仅智能算法）":
            default_brand_algorithm = "智能词组识别"
            default_ngram = False
            default_show_filtered = True
            default_core_recommendation = True
            default_negative_option = "在默认基础上修改"
        else:  # 自定义配置
            default_brand_algorithm = "智能词组识别"
            default_ngram = True
            default_show_filtered = True
            default_core_recommendation = True
            default_negative_option = "在默认基础上修改"
        
        # 紧凑型高级选项
        st.markdown("#### 🔬 高级选项")
        
        # 使用紧凑的列布局
        adv_col1, adv_col2 = st.columns(2)
        with adv_col1:
            show_filtered_keywords = st.checkbox("显示过滤词", value=default_show_filtered, help="显示被过滤的关键词")
        with adv_col2:
            enable_core_recommendation = st.checkbox("核心词推荐", value=default_core_recommendation, help="启用核心关键词推荐")
        
        # 核心品牌词算法选择
        st.markdown("#### 🎯 品牌词算法")
        
        # 根据预设或允许自定义
        if preset == "自定义配置":
            brand_algorithm = st.selectbox(
                "选择品牌词识别算法",
                options=["智能词组识别", "传统单词识别"],
                index=0 if default_brand_algorithm == "智能词组识别" else 1,
                help="智能词组识别：能识别'韩语输入法'等完整品牌词组\n传统单词识别：基于单词频率的简单算法"
            )
            
            # n-gram分析选项
            enable_ngram = st.checkbox(
                "启用n-gram分析",
                value=default_ngram,
                help="使用2-gram、3-gram等连续组合的频率统计，提高品牌词识别准确性"
            )
        else:
            # 显示预设配置但不允许修改
            brand_algorithm = default_brand_algorithm
            enable_ngram = default_ngram
            
            st.info(f"🎯 算法设置：{brand_algorithm}")
            if enable_ngram:
                st.info("🔬 n-gram分析：已启用")
            else:
                st.info("🔬 n-gram分析：已禁用")
            
            if st.button("🔧 切换到自定义配置", key="switch_to_custom"):
                st.rerun()

        if enable_ngram:
            st.info("🔬 n-gram分析将识别连续字符和词组的模式，帮助发现更准确的品牌词组合")

    # 主内容区域 - 使用标签页分离功能
    tab1, tab2, tab3, tab4 = st.tabs(["🔍 关键词分组", "🔄 百度转Bing", "🚀 必应错拼词", "🔑 关键词ID同步"])
    
    with tab1:
        st.header("📂 自动化关键词分组功能")
        
        # 关键词文件上传 - 优化版
        st.markdown("#### 📁 关键词文件")
        
        uploaded_keywords_file = st.file_uploader(
            "选择文件上传",
            type=['txt', 'csv', 'xlsx'],
            help="支持TXT、CSV、XLSX格式，文件大小限制200MB",
            key="keywords_uploader",
            label_visibility="collapsed"
        )
        
        # 文件状态显示
        if uploaded_keywords_file is not None:
            st.success(f"✅ 已上传：**{uploaded_keywords_file.name}** ({uploaded_keywords_file.size / 1024:.1f} KB)")
        else:
            st.info("💡 请上传关键词文件开始分析")
        
        # 紧凑型否定词设置
        st.markdown("#### 🚫 否定词设置")
        
        # 否定词设置选项
        options = ["使用默认否定词", "在默认基础上修改", "完全自定义", "不使用否定词"]
        default_index = options.index(default_negative_option) if preset != "自定义配置" else 1
        
        negative_word_option = st.radio(
            "选择否定词设置方式",
            options=options,
            index=default_index,
            horizontal=True,
            help="推荐选择'在默认基础上修改'，可以根据您的需求调整否定词"
        )
        
        negative_words_input = ""
        
        if negative_word_option == "使用默认否定词":
            # 加载默认否定词
            default_negative_words = load_default_negative_words()
            negative_words_input = default_negative_words
            
            with st.expander("📝 查看默认否定词", expanded=False):
                negative_words_list = [word.strip() for word in default_negative_words.split('\n') if word.strip()]
                st.write(f"共 {len(negative_words_list)} 个默认否定词：")
                
                # 分列显示否定词
                cols = st.columns(4)
                for i, word in enumerate(negative_words_list):
                    with cols[i % 4]:
                        st.write(f"• {word}")
                        
        elif negative_word_option == "在默认基础上修改":
            # 加载默认否定词作为初始值
            default_negative_words = load_default_negative_words()
            
            st.info("💡 在下面的文本框中修改否定词列表，已预填默认否定词")
            
            negative_words_input = st.text_area(
                "否定词列表（一行一个）",
                value=default_negative_words,
                height=200,
                help="已预填默认否定词，您可以添加、删除或修改。包含这些词的关键词将被过滤掉。",
                placeholder="请在默认否定词基础上进行修改..."
            )
            
            # 操作按钮
            button_col1, button_col2 = st.columns(2)
            
            with button_col1:
                if st.button("🔄 重置为默认", key="reset_negative_words"):
                    negative_words_input = default_negative_words
                    st.success("✅ 已重置为默认否定词")
                    st.rerun()
            
            with button_col2:
                # 导出当前否定词
                if negative_words_input.strip():
                    st.download_button(
                        label="📥 导出否定词",
                        data=negative_words_input,
                        file_name=f"negative_words_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                        mime="text/plain",
                        key="export_negative_words"
                    )
            
        elif negative_word_option == "完全自定义":
            # 使用session state来管理自定义否定词
            if 'custom_negative_words' not in st.session_state:
                st.session_state.custom_negative_words = ""
            
            negative_words_input = st.text_area(
                "否定词列表（一行一个）",
                value=st.session_state.custom_negative_words,
                height=150,
                help="完全自定义否定词列表。包含这些词的关键词将被过滤掉。",
                placeholder="请输入否定词，每行一个...",
                key="custom_negative_input"
            )
            
            # 更新session state
            st.session_state.custom_negative_words = negative_words_input
            
            # 一行操作按钮 - 移除导入功能
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("📋 加载默认", key="load_default", use_container_width=True):
                    default_words = load_default_negative_words()
                    st.session_state.custom_negative_words = default_words
                    st.success("✅ 已加载默认否定词")
                    st.rerun()
            
            with col2:
                if st.button("🧹 清空全部", key="clear_all", use_container_width=True):
                    st.session_state.custom_negative_words = ""
                    st.success("✅ 已清空所有否定词")
                    st.rerun()
            
            with col3:
                if st.button("🔤 去重排序", key="dedupe_sort", use_container_width=True):
                    if negative_words_input.strip():
                        words = list(set([word.strip() for word in negative_words_input.split('\n') if word.strip()]))
                        words.sort()
                        st.session_state.custom_negative_words = '\n'.join(words)
                        st.success("✅ 已去重并排序")
                        st.rerun()
                    else:
                        st.warning("⚠️ 没有内容可以排序")
                
                # 显示当前否定词统计
                if negative_words_input.strip():
                    words_list = [word.strip() for word in negative_words_input.split('\n') if word.strip()]
                    unique_words = set(words_list)
                    duplicate_count = len(words_list) - len(unique_words)
                    
                    # 使用更美观的统计显示
                    stat_col1, stat_col2, stat_col3 = st.columns(3)
                    with stat_col1:
                        st.metric("总词数", len(words_list))
                    with stat_col2:
                        st.metric("唯一词数", len(unique_words))
                    with stat_col3:
                        if duplicate_count > 0:
                            st.metric("重复词数", duplicate_count, delta=f"-{duplicate_count}", delta_color="inverse")
                        else:
                            st.metric("重复词数", 0, delta="✓ 无重复")
                    
                    # 如果有重复词，提供去重建议
                    if duplicate_count > 0:
                        st.warning(f"⚠️ 发现 {duplicate_count} 个重复词，建议使用'去重排序'功能清理")
                
                # 使用提示
                st.info("💡 **使用提示：** 请手动输入否定词，每行一个。可以使用上方按钮进行基本操作。")
        
        # 显示否定词统计
        if negative_words_input.strip():
            negative_words_list = [word.strip() for word in negative_words_input.split('\n') if word.strip()]
            st.success(f"✅ 已设置 {len(negative_words_list)} 个否定词")
        else:
            st.info("📝 未设置否定词，将不进行否定词过滤")

        # 核心品牌词设置
        st.markdown("#### 🎯 核心品牌词")
        core_brand_option = st.radio(
            "设置核心品牌词",
            options=["自动识别", "手动指定"],
            index=0,
            horizontal=True,
            help="自动识别品牌词或手动指定一个核心词"
        )
        manual_core_brand = ""
        if core_brand_option == "手动指定":
            manual_core_brand = st.text_input(
                "输入核心品牌词",
                placeholder="例如: potplayer",
                help="手动输入您定义的核心品牌词"
            )
        
        # 紧凑型处理按钮区域
        st.markdown("#### 🚀 开始处理")
        
        # 检查是否可以开始处理
        can_process = uploaded_keywords_file is not None
        
        if can_process:
            # 简洁的处理摘要
            negative_count = len([word.strip() for word in negative_words_input.split('\n') if word.strip()]) if negative_words_input.strip() else 0
            
            st.markdown(f"""
            <div class="inline-tip">
                📊 {uploaded_keywords_file.name} | 🎯 {brand_algorithm} | 🔬 n-gram {'✓' if enable_ngram else '✗'} | 🚫 {negative_count}个否定词
            </div>
            """, unsafe_allow_html=True)
        
        # 处理按钮
        process_button = st.button(
            "🚀 开始智能分组" if can_process else "⚠️ 请先上传文件", 
            type="primary" if can_process else "secondary",
            use_container_width=True,
            disabled=not can_process,
            help="Ctrl+Enter" if can_process else "请先上传关键词文件"
        )
        
        if process_button:
            if can_process:
                # 处理否定词输入
                negative_words_list = []
                if negative_words_input.strip():
                    negative_words_list = [word.strip() for word in negative_words_input.split('\n') if word.strip()]
                
                # 调用处理函数，传递所有必需的参数
                process_keywords(
                    uploaded_keywords_file, 
                    negative_words_list,
                    min_length,
                    max_length,
                    show_filtered_keywords,
                    enable_core_recommendation,
                    brand_algorithm,
                    enable_ngram,
                    core_brand_option,
                    manual_core_brand
                )
            else:
                st.error("❌ 请先上传关键词文件！")
    
    # 百度转Bing功能标签页
    with tab2:
        st.header("🔄 百度SEM关键词转Bing Ads格式")
        
        # 功能介绍
        st.markdown("""
        <div class="feature-intro">
            <h4>🎯 功能介绍</h4>
            <p>将百度SEM关键词文件（CSV或Excel）转换为Bing Ads上传格式，支持：</p>
            <ul>
                <li>• 匹配模式转换（广泛匹配、短语匹配、精确匹配）</li>
                <li>• URL解析和转换</li>
                <li>• 多编码支持（UTF-8、GBK、GB2312等）</li>
                <li>• 数据清洗和去重</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
        
        # 文件上传
        st.markdown("#### 📁 上传百度SEM文件")
        
        uploaded_baidu_file = st.file_uploader(
            "选择百度SEM关键词文件",
            type=['csv', 'xlsx'],
            help="支持CSV和Excel格式，文件大小限制200MB",
            key="baidu_uploader"
        )
        
        # 文件状态显示
        if uploaded_baidu_file is not None:
            st.success(f"✅ 已上传：**{uploaded_baidu_file.name}** ({uploaded_baidu_file.size / 1024:.1f} KB)")
            
            # 显示文件预览
            with st.expander("🔍 文件预览", expanded=False):
                try:
                    # 保存临时文件
                    temp_file_path = f"temp_{uploaded_baidu_file.name}"
                    with open(temp_file_path, "wb") as f:
                        f.write(uploaded_baidu_file.getbuffer())
                    
                    # 创建转换器实例并预览
                    from baidu_to_bing_converter import BaiduToBingConverter
                    converter = BaiduToBingConverter()
                    preview_df = converter._read_file_with_encoding_fallback(temp_file_path)
                    
                    if not preview_df.empty:
                        st.write(f"📊 数据行数：{len(preview_df)}")
                        st.write("🔍 前5行数据预览：")
                        st.dataframe(preview_df.head(), use_container_width=True)
                        
                        # 显示列名
                        st.write("📝 数据列：")
                        cols = st.columns(min(len(preview_df.columns), 4))
                        for i, col in enumerate(preview_df.columns):
                            with cols[i % 4]:
                                st.write(f"• {col}")
                    else:
                        st.warning("⚠️ 文件为空或格式不正确")
                    
                    # 清理临时文件
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)
                        
                except Exception as e:
                    st.error(f"文件预览失败：{str(e)}")
        else:
            st.info("💡 请上传百度SEM关键词文件")
        
        # 转换设置
        st.markdown("#### ⚙️ 转换设置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 模板信息
            st.markdown("**模板信息**")
            try:
                # 获取模板信息
                grouper = KeywordGrouper()
                template_info = grouper.get_baidu_template_info()
                
                st.success(f"✅ 上传模板：{template_info['upload_template']}")
                st.success(f"✅ 导出模板：{template_info['export_template']}")
                        
            except Exception as e:
                st.warning(f"⚠️ 模板加载失败：{str(e)}")
        
        with col2:
            # 转换选项
            st.markdown("**转换选项**")
            
            enable_url_parsing = st.checkbox(
                "🔗 启用URL解析",
                value=True,
                help="解析百度链接中的目标URL"
            )
            
            enable_data_cleaning = st.checkbox(
                "🧹 启用数据清洗",
                value=True,
                help="去除空行、重复数据和无效内容"
            )
            
            preserve_original_data = st.checkbox(
                "💾 保留原始数据",
                value=False,
                help="在转换结果中保留原始百度数据列"
            )
        
        # 转换按钮
        st.markdown("#### 🚀 开始转换")
        
        can_convert = uploaded_baidu_file is not None
        
        if can_convert:
            # 转换摘要
            st.markdown(f"""
            <div class="inline-tip">
                📁 {uploaded_baidu_file.name} | 🔗 URL解析 {'✓' if enable_url_parsing else '✗'} | 🧹 数据清洗 {'✓' if enable_data_cleaning else '✗'}
            </div>
            """, unsafe_allow_html=True)
        
        convert_button = st.button(
            "🚀 开始转换" if can_convert else "⚠️ 请先上传文件",
            type="primary" if can_convert else "secondary",
            use_container_width=True,
            disabled=not can_convert,
            help="将百度SEM文件转换为Bing Ads格式" if can_convert else "请先上传百度SEM文件"
        )
        
        if convert_button and can_convert:
            # 执行转换
            with st.spinner("🔄 正在转换中..."):
                try:
                    # 执行转换
                    grouper = KeywordGrouper()
                    result_df = grouper.convert_baidu_to_bing_with_options(
                        uploaded_baidu_file,
                        enable_url_parsing=enable_url_parsing,
                        enable_data_cleaning=enable_data_cleaning,
                        preserve_original_data=preserve_original_data
                    )
                    
                    if result_df is not None and not result_df.empty:
                        st.success(f"✅ 转换成功！共处理 {len(result_df)} 行数据")
                        
                        # 显示转换结果预览
                        with st.expander("🔍 转换结果预览", expanded=True):
                            st.write(f"📊 转换后数据行数：{len(result_df)}")
                            st.write("🔍 前10行数据预览：")
                            st.dataframe(result_df.head(10), use_container_width=True)
                            
                            # 显示列统计
                            st.write("📝 转换后数据列：")
                            cols = st.columns(min(len(result_df.columns), 4))
                            for i, col in enumerate(result_df.columns):
                                with cols[i % 4]:
                                    st.write(f"• {col}")
                        
                        # 生成下载文件
                        
                        # 生成Excel文件
                        output = io.BytesIO()
                        with pd.ExcelWriter(output, engine='openpyxl') as writer:
                            result_df.to_excel(writer, index=False, sheet_name='Bing_Keywords')
                        excel_data = output.getvalue()
                        
                        # 下载按钮
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        filename = f"bing_keywords_{timestamp}.xlsx"
                        
                        st.download_button(
                            label="📥 下载Bing Ads文件",
                            data=excel_data,
                            file_name=filename,
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            use_container_width=True
                        )
                        
                        st.success("✅ 转换完成！点击上方按钮下载结果文件")
                        
                    else:
                        st.error("❌ 转换失败：未能生成有效数据")
                        
                except Exception as e:
                    st.error(f"❌ 转换失败：{str(e)}")
                    st.write("🔍 详细错误信息：")
                    st.code(str(e))
                    
                finally:
                    # 清理临时文件（新方法内部处理）
                    pass
        
        # 使用说明
        with st.expander("📚 使用说明", expanded=False):
            st.markdown("""
            ### 使用步骤：
            1. **上传文件**：选择百度SEM导出的关键词文件（CSV或Excel格式）
            2. **设置选项**：根据需要选择转换选项
            3. **开始转换**：点击转换按钮，等待处理完成
            4. **下载结果**：下载转换后的Bing Ads文件
            
            ### 支持的数据格式：
            - **输入**：CSV、Excel (.xlsx)
            - **输出**：Excel (.xlsx)，兼容Bing Ads上传格式
            
            ### 注意事项：
            - 确保上传的文件包含必要的列（关键词、匹配模式等）
            - 建议启用数据清洗选项以获得更好的结果
            - 转换后请检查数据的准确性
            """)
    
    # 关键词扩展功能标签页
    with tab3:
        st.header("🚀 关键词扩展生成器")
        
        # 功能介绍
        st.markdown("""
        <div class="feature-intro">
            <h4>🎯 功能介绍</h4>
            <p>根据基础关键词和模板文件生成扩展关键词，支持：</p>
            <ul>
                <li>• 基于模板文件的关键词扩展</li>
                <li>• 批量关键词处理</li>
                <li>• 自定义推广计划和广告组</li>
                <li>• 支持自定义模板上传</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
        
        # 配置设置
        st.markdown("#### ⚙️ 扩展配置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**基础设置**")
            
            campaign_name = st.text_input(
                "推广计划名称",
                value="第三方软件-potplayer",
                help="设置推广计划的名称"
            )
            
            ad_group_name = st.text_input(
                "广告组名称",
                value="错拼词",
                help="设置广告组的名称"
            )
            
            match_type = st.selectbox(
                "匹配模式",
                options=["Exact", "Phrase", "Broad"],
                index=0,
                help="选择关键词匹配模式"
            )
            
            bid_price = st.text_input(
                "出价",
                value="0.39",
                help="设置关键词出价"
            )
        
        with col2:
            st.markdown("**URL设置**")
            
            final_url = st.text_input(
                "最终访问网址",
                value="https://sem.duba.net/sem/dseek/f255.html?sfrom=196&TFT=16&keyID=144553",
                help="设置关键词的最终访问网址"
            )
            
            st.markdown("**模板文件**")
            
            # 模板文件选择
            use_custom_template = st.checkbox(
                "使用自定义模板",
                value=False,
                help="是否上传自定义模板文件"
            )
            
            uploaded_template = None
            if use_custom_template:
                uploaded_template = st.file_uploader(
                    "上传模板文件",
                    type=['xlsx'],
                    help="上传包含'keys'列的Excel模板文件",
                    key="template_uploader"
                )
                
                if uploaded_template:
                    st.success(f"✅ 已上传模板：**{uploaded_template.name}**")
            else:
                if os.path.exists('codigo.xlsx'):
                    st.success("✅ 使用默认模板：codigo.xlsx")
                else:
                    st.warning("⚠️ 默认模板文件不存在：codigo.xlsx")
        
        # 关键词输入
        st.markdown("#### 📝 基础关键词")
        
        # 关键词输入方式选择
        input_method = st.radio(
            "选择输入方式",
            options=["手动输入", "文件上传"],
            horizontal=True,
            help="选择关键词的输入方式"
        )
        
        base_keywords = []
        
        if input_method == "手动输入":
            keywords_text = st.text_area(
                "输入基础关键词（一行一个）",
                value="potplayer\npotplayer播放器\npotplayer下载",
                height=150,
                help="每行输入一个基础关键词，这些关键词将用于扩展生成"
            )
            
            if keywords_text.strip():
                base_keywords = [kw.strip() for kw in keywords_text.split('\n') if kw.strip()]
                st.info(f"📝 已输入 {len(base_keywords)} 个基础关键词")
        
        else:  # 文件上传
            uploaded_keywords_file = st.file_uploader(
                "上传关键词文件",
                type=['txt', 'csv', 'xlsx'],
                help="上传包含基础关键词的文件",
                key="keywords_file_uploader"
            )
            
            if uploaded_keywords_file:
                try:
                    # 根据文件类型读取关键词
                    if uploaded_keywords_file.name.endswith('.txt'):
                        content = uploaded_keywords_file.read().decode('utf-8')
                        base_keywords = [kw.strip() for kw in content.split('\n') if kw.strip()]
                    elif uploaded_keywords_file.name.endswith('.csv'):
                        df = pd.read_csv(uploaded_keywords_file)
                        # 尝试从第一列获取关键词
                        base_keywords = df.iloc[:, 0].dropna().astype(str).tolist()
                    elif uploaded_keywords_file.name.endswith(('.xlsx', '.xls')):
                        df = pd.read_excel(uploaded_keywords_file)
                        # 尝试从第一列获取关键词
                        base_keywords = df.iloc[:, 0].dropna().astype(str).tolist()
                    
                    st.success(f"✅ 从文件中读取到 {len(base_keywords)} 个基础关键词")
                    
                    # 显示前几个关键词作为预览
                    if base_keywords:
                        preview_count = min(5, len(base_keywords))
                        st.write("🔍 关键词预览：")
                        for i, kw in enumerate(base_keywords[:preview_count]):
                            st.write(f"• {kw}")
                        if len(base_keywords) > preview_count:
                            st.write(f"... 还有 {len(base_keywords) - preview_count} 个关键词")
                    
                except Exception as e:
                    st.error(f"❌ 读取文件失败：{str(e)}")
        
        # 扩展按钮
        st.markdown("#### 🚀 开始扩展")
        
        can_expand = len(base_keywords) > 0 and (not use_custom_template or uploaded_template is not None)
        
        if can_expand:
            # 扩展摘要
            template_info = "自定义模板" if use_custom_template else "默认模板"
            st.markdown(f"""
            <div class="inline-tip">
                📊 {len(base_keywords)}个基础关键词 | 📁 {template_info} | 🎯 {match_type}匹配 | 💰 {bid_price}出价
            </div>
            """, unsafe_allow_html=True)
        
        expand_button = st.button(
            "🚀 开始扩展关键词" if can_expand else "⚠️ 请完成配置", 
            type="primary" if can_expand else "secondary",
            use_container_width=True,
            disabled=not can_expand,
            help="根据基础关键词和模板生成扩展关键词" if can_expand else "请输入基础关键词并选择模板"
        )
        
        if expand_button and can_expand:
            # 执行关键词扩展
            with st.spinner("🔄 正在扩展关键词..."):
                try:
                    from keyword_expander import KeywordExpander
                    
                    expander = KeywordExpander()
                    
                    # 准备配置
                    config = {
                        "match_type": match_type,
                        "bid": bid_price,
                        "campaign": campaign_name,
                        "ad_group": ad_group_name,
                        "url": final_url,
                        "keywords": base_keywords
                    }
                    
                    # 根据是否使用自定义模板选择处理方式
                    if use_custom_template and uploaded_template:
                        # 使用上传的模板
                        success, count, result_df = expander.expand_from_upload(uploaded_template, config)
                        
                        if success and result_df is not None:
                            st.success(f"✅ 扩展成功！共生成 {count} 个关键词")
                            
                            # 显示结果预览
                            with st.expander("🔍 扩展结果预览", expanded=True):
                                st.write(f"📊 扩展后关键词数量：{len(result_df)}")
                                st.write("🔍 前10行数据预览：")
                                st.dataframe(result_df.head(10), use_container_width=True)
                                
                                # 显示统计信息
                                col1, col2, col3 = st.columns(3)
                                with col1:
                                    st.metric("基础关键词", len(base_keywords))
                                with col2:
                                    st.metric("扩展关键词", count)
                                with col3:
                                    expansion_rate = round(count / len(base_keywords), 1)
                                    st.metric("扩展倍数", f"{expansion_rate}x")
                            
                            # 生成下载文件
                            output = io.BytesIO()
                            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                                result_df.to_excel(writer, index=False, sheet_name='Extended_Keywords')
                            excel_data = output.getvalue()
                            
                            # 下载按钮
                            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                            filename = f"expanded_keywords_{timestamp}.xlsx"
                            
                            st.download_button(
                                label="📥 下载扩展关键词",
                                data=excel_data,
                                file_name=filename,
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                use_container_width=True
                            )
                            
                            st.success("✅ 扩展完成！点击上方按钮下载结果文件")
                        else:
                            st.error("❌ 扩展失败：未能生成有效数据")
                    
                    else:
                        # 使用默认模板
                        config['codigo_file'] = 'codigo.xlsx'
                        config['output_file'] = '1.1-关键词上传模版.xlsx'
                        
                        # 检查必要文件
                        if not os.path.exists(config['codigo_file']):
                            st.error(f"❌ 模板文件不存在: {config['codigo_file']}")
                            return
                            
                        if not os.path.exists(config['output_file']):
                            st.error(f"❌ 输出模板文件不存在: {config['output_file']}")
                            return
                        
                        success, count, output_file = expander.expand_keywords(config)
                        
                        if success:
                            st.success(f"✅ 扩展成功！共生成 {count} 个关键词")
                            st.info(f"📁 结果已保存到：{output_file}")
                            
                            # 读取并显示结果
                            if os.path.exists(output_file):
                                result_df = pd.read_excel(output_file)
                                
                                # 显示结果预览
                                with st.expander("🔍 扩展结果预览", expanded=True):
                                    st.write(f"📊 扩展后关键词数量：{len(result_df)}")
                                    st.write("🔍 前10行数据预览：")
                                    st.dataframe(result_df.head(10), use_container_width=True)
                                    
                                    # 显示统计信息
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.metric("基础关键词", len(base_keywords))
                                    with col2:
                                        st.metric("扩展关键词", count)
                                    with col3:
                                        expansion_rate = round(count / len(base_keywords), 1)
                                        st.metric("扩展倍数", f"{expansion_rate}x")
                                
                                # 提供下载
                                with open(output_file, 'rb') as f:
                                    excel_data = f.read()
                                
                                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                filename = f"expanded_keywords_{timestamp}.xlsx"
                                
                                st.download_button(
                                    label="📥 下载扩展关键词",
                                    data=excel_data,
                                    file_name=filename,
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                    use_container_width=True
                                )
                            else:
                                st.error(f"❌ 输出文件不存在: {output_file}")
                        else:
                            st.error("❌ 扩展失败")
                    
                except Exception as e:
                    st.error(f"❌ 扩展失败：{str(e)}")
        
        # 使用说明
        with st.expander("📚 使用说明", expanded=False):
            st.markdown("""
            ### 使用步骤：
            1. **配置设置**：设置推广计划名称、广告组名称、匹配模式等
            2. **选择模板**：使用默认模板或上传自定义模板文件
            3. **输入关键词**：手动输入或上传包含基础关键词的文件
            4. **开始扩展**：点击扩展按钮，等待处理完成
            5. **下载结果**：下载扩展后的关键词文件
            
            ### 模板文件格式：
            - **文件类型**：Excel (.xlsx)
            - **必需列**：包含名为'keys'的列
            - **模板规则**：在'keys'列中使用"关键词"作为占位符，系统会自动替换为实际的基础关键词
            
            ### 示例模板内容：
            ```
            keys
            关键词
            关键词下载
            关键词官网
            关键词安装
            免费关键词
            ```
            
            ### 注意事项：
            - 基础关键词将替换模板中的"关键词"占位符
            - 扩展后的关键词会自动去重
            - 建议先用少量关键词测试模板效果
            """)

    with tab4:
        st.header("🔑 关键词ID同步")
        
        # 功能介绍
        st.markdown("""
        <div class="feature-intro">
            <h4>🎯 功能介绍</h4>
            <p>将上传的关键词与数据库同步，自动获取或创建关键词ID，支持：</p>
            <ul>
                <li>• 自动识别已存在的关键词并获取其ID</li>
                <li>• 自动插入新关键词并生成ID</li>
                <li>• 识别未找到推广计划的关键词</li>
                <li>• 生成完整的同步报告</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

        # Use session state to store the sync results
        if 'sync_results' not in st.session_state:
            st.session_state.sync_results = None

        st.markdown("#### 📁 上传关键词文件")
        uploaded_sync_file = st.file_uploader(
            "选择包含'推广计划'、'推广组'和'关键词'列的文件", 
            type=['xlsx', 'csv'], 
            key="sync_uploader",
            help="文件必须包含：推广计划、推广组、关键词 三列"
        )

        if uploaded_sync_file:
            st.success(f"✅ 已上传文件: **{uploaded_sync_file.name}** ({uploaded_sync_file.size / 1024:.1f} KB)")
            
            # 显示文件预览
            with st.expander("🔍 文件预览", expanded=False):
                try:
                    if uploaded_sync_file.name.endswith('.csv'):
                        preview_df = pd.read_csv(uploaded_sync_file)
                    else:
                        preview_df = pd.read_excel(uploaded_sync_file)
                    
                    st.write(f"📊 数据行数：{len(preview_df)}")
                    st.write("🔍 前5行数据预览：")
                    st.dataframe(preview_df.head(), use_container_width=True)
                    
                    # 检查必需列
                    required_columns = ["推广计划", "推广组", "关键词"]
                    missing_columns = [col for col in required_columns if col not in preview_df.columns]
                    
                    if missing_columns:
                        st.error(f"❌ 缺少必需列: {', '.join(missing_columns)}")
                    else:
                        st.success("✅ 文件格式正确，包含所有必需列")
                    
                except Exception as e:
                    st.error(f"文件预览失败：{str(e)}")
        else:
            st.info("💡 请上传包含关键词数据的文件")

        # 同步配置
        st.markdown("#### ⚙️ 同步配置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**数据库连接**")
            if os.path.exists('sem.csv'):
                st.success("✅ 推广计划映射文件 (sem.csv) 已就绪")
            else:
                st.error("❌ 未找到推广计划映射文件 (sem.csv)")
                st.info("💡 请确保 sem.csv 文件存在于项目目录中")
        
        with col2:
            st.markdown("**同步选项**")
            
            show_details = st.checkbox(
                "显示详细结果",
                value=True,
                help="显示详细的同步结果，包括已存在的关键词和未找到的关键词"
            )
            
            export_all_data = st.checkbox(
                "导出所有数据",
                value=False,
                help="导出包含未找到关键词的完整数据表"
            )

        st.markdown("#### 🚀 开始同步")
        
        can_sync = uploaded_sync_file is not None and os.path.exists('sem.csv')
        
        if can_sync:
            st.markdown(f"""
            <div class="inline-tip">
                📁 {uploaded_sync_file.name} | 📊 数据同步 | 🔍 详细结果 {'✓' if show_details else '✗'}
            </div>
            """, unsafe_allow_html=True)
        
        sync_button = st.button(
            "🚀 开始同步关键词" if can_sync else "⚠️ 请完成配置",
            type="primary" if can_sync else "secondary",
            use_container_width=True,
            disabled=not can_sync,
            help="开始同步关键词并获取ID" if can_sync else "请上传文件并确保配置文件存在"
        )

        if sync_button and can_sync:
            # Reset previous results
            st.session_state.sync_results = None
            
            with st.spinner("🔄 正在同步关键词..."):
                syncer = KeywordIdSyncer()
                
                try:
                    # 连接数据库
                    status = syncer.connect()
                    if "成功" in status:
                        st.success(status)
                        
                        # 执行同步
                        message, result_df, sync_results = syncer.sync_keywords(uploaded_sync_file, "sem.csv")
                        
                        # 保存结果到session state
                        st.session_state.sync_results = {
                            'message': message,
                            'result_df': result_df,
                            'sync_results': sync_results
                        }
                        
                        # 显示同步结果
                        st.success(message)
                        
                        # 显示统计信息
                        if sync_results:
                            col1, col2, col3, col4 = st.columns(4)
                            
                            with col1:
                                st.metric("新增关键词", sync_results['inserted_count'])
                            
                            with col2:
                                st.metric("已存在关键词", sync_results['existing_count'])
                            
                            with col3:
                                missing_count = len(sync_results['missing_softid']) if not sync_results['missing_softid'].empty else 0
                                st.metric("未找到推广计划", missing_count)
                            
                            with col4:
                                empty_count = len(sync_results['empty_keywords']) if not sync_results['empty_keywords'].empty else 0
                                st.metric("空关键词", empty_count)
                        
                        # 显示成功获取ID的关键词
                        if result_df is not None and not result_df.empty:
                            st.subheader("✅ 成功获取ID的关键词")
                            st.dataframe(result_df, use_container_width=True)
                        
                        # 显示详细结果
                        if show_details and sync_results:
                            # 显示已存在的关键词
                            if not sync_results['existing_keywords'].empty:
                                with st.expander(f"📋 已存在的关键词 ({len(sync_results['existing_keywords'])} 个)", expanded=False):
                                    st.dataframe(sync_results['existing_keywords'], use_container_width=True)
                            
                            # 显示新插入的关键词
                            if not sync_results['new_keywords'].empty:
                                with st.expander(f"🆕 新插入的关键词 ({len(sync_results['new_keywords'])} 个)", expanded=False):
                                    st.dataframe(sync_results['new_keywords'], use_container_width=True)
                            
                            # 显示未找到推广计划的关键词
                            if not sync_results['missing_softid'].empty:
                                with st.expander(f"⚠️ 未找到推广计划的关键词 ({len(sync_results['missing_softid'])} 个)", expanded=True):
                                    st.warning("这些关键词无法找到对应的推广计划ID，请检查推广计划名称是否正确")
                                    # 只显示相关列
                                    display_cols = ['推广计划', '推广组', '关键词']
                                    available_cols = [col for col in display_cols if col in sync_results['missing_softid'].columns]
                                    st.dataframe(sync_results['missing_softid'][available_cols], use_container_width=True)
                                    
                                    # 显示未找到的推广计划列表
                                    missing_plans = sync_results['missing_softid']['推广计划'].unique()
                                    st.write("📝 未找到的推广计划：")
                                    for plan in missing_plans:
                                        st.write(f"• {plan}")
                            
                            # 显示空关键词的行
                            if not sync_results['empty_keywords'].empty:
                                with st.expander(f"🔍 空关键词的行 ({len(sync_results['empty_keywords'])} 个)", expanded=False):
                                    st.info("这些行的关键词字段为空")
                                    display_cols = ['推广计划', '推广组', '关键词']
                                    available_cols = [col for col in display_cols if col in sync_results['empty_keywords'].columns]
                                    st.dataframe(sync_results['empty_keywords'][available_cols], use_container_width=True)
                    
                    else:
                        st.error(status)
                        
                except Exception as e:
                    st.error(f"❌ 同步失败：{str(e)}")
                    with st.expander("🔍 错误详情", expanded=False):
                        st.exception(e)
                finally:
                    syncer.close()

        # 导出功能
        if st.session_state.sync_results is not None:
            st.markdown("---")
            st.header("📥 导出同步结果")
            
            sync_data = st.session_state.sync_results
            result_df = sync_data['result_df']
            sync_results = sync_data['sync_results']
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**📊 成功获取ID的关键词**")
                
                if result_df is not None and not result_df.empty:
                    # 生成Excel文件
                    output = io.BytesIO()
                    with pd.ExcelWriter(output, engine='openpyxl') as writer:
                        result_df.to_excel(writer, index=False, sheet_name='Synced_Keywords')
                    excel_data = output.getvalue()

                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"synced_keywords_{timestamp}.xlsx"

                    st.download_button(
                        label="📥 下载成功同步的关键词",
                        data=excel_data,
                        file_name=filename,
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        use_container_width=True,
                        key="download_synced_keywords"
                    )
                    
                    st.success(f"✅ 可下载 {len(result_df)} 个成功同步的关键词")
                else:
                    st.info("📝 没有成功同步的关键词可下载")
            
            with col2:
                st.markdown("**📋 完整同步报告**")
                
                if export_all_data and sync_results:
                    # 生成完整报告
                    output = io.BytesIO()
                    with pd.ExcelWriter(output, engine='openpyxl') as writer:
                        # 成功同步的关键词
                        if result_df is not None and not result_df.empty:
                            result_df.to_excel(writer, index=False, sheet_name='成功同步')
                        
                        # 已存在的关键词
                        if not sync_results['existing_keywords'].empty:
                            sync_results['existing_keywords'].to_excel(writer, index=False, sheet_name='已存在关键词')
                        
                        # 新插入的关键词
                        if not sync_results['new_keywords'].empty:
                            sync_results['new_keywords'].to_excel(writer, index=False, sheet_name='新插入关键词')
                        
                        # 未找到推广计划的关键词
                        if not sync_results['missing_softid'].empty:
                            sync_results['missing_softid'].to_excel(writer, index=False, sheet_name='未找到推广计划')
                        
                        # 空关键词
                        if not sync_results['empty_keywords'].empty:
                            sync_results['empty_keywords'].to_excel(writer, index=False, sheet_name='空关键词')
                    
                    complete_excel_data = output.getvalue()
                    complete_filename = f"complete_sync_report_{timestamp}.xlsx"
                    
                    st.download_button(
                        label="📥 下载完整同步报告",
                        data=complete_excel_data,
                        file_name=complete_filename,
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        use_container_width=True,
                        key="download_complete_report"
                    )
                    
                    st.success("✅ 完整报告包含所有同步结果")
                else:
                    if not export_all_data:
                        st.info("💡 勾选'导出所有数据'以生成完整报告")
                    else:
                        st.info("📝 没有额外数据可导出")
        
        # 使用说明
        with st.expander("📚 使用说明", expanded=False):
            st.markdown("""
            ### 使用步骤：
            1. **准备文件**：确保上传的文件包含"推广计划"、"推广组"、"关键词"三列
            2. **检查配置**：确保 sem.csv 文件存在于项目目录中
            3. **上传文件**：选择要同步的关键词文件
            4. **开始同步**：点击同步按钮，等待处理完成
            5. **查看结果**：查看同步统计和详细结果
            6. **导出数据**：下载成功同步的关键词或完整报告
            
            ### 文件格式要求：
            - **输入文件**：CSV或Excel格式
            - **必需列**：推广计划、推广组、关键词
            - **配置文件**：sem.csv（推广计划ID映射）
            
            ### 同步逻辑：
            - **已存在**：如果关键词已在数据库中，直接获取其ID
            - **新关键词**：如果关键词不存在，自动插入并获取新ID
            - **未找到推广计划**：如果推广计划名称在sem.csv中不存在，无法同步
            - **空关键词**：关键词字段为空的行会被跳过
            
            ### 注意事项：
            - 确保推广计划名称与sem.csv中的计划名完全匹配
            - 关键词比较时会自动去除空格并忽略大小写
            - 建议先用少量数据测试同步效果
            """)
        


def process_keywords(keywords_file, negative_words_list, min_length, max_length, show_filtered, enable_core_recommendation, brand_algorithm, enable_ngram=True, core_brand_option="自动识别", manual_core_brand=""):
    """处理关键词分组 - 完全按照keyword_grouper.py的流程"""
    
    # 创建处理容器
    with st.container():
        st.markdown("---")
        st.markdown("## 🔄 处理进度")
        
        # 创建进度显示区域
        progress_container = st.container()
        
        with progress_container:
            # 显示处理进度
            progress_bar = st.progress(0)
            status_text = st.empty()
            time_text = st.empty()
            
            start_time = datetime.now()
            
        try:
            # 初始化分组器
            status_text.markdown("**🔧 正在初始化分组器...**")
            progress_bar.progress(5)
            
            grouper = KeywordGrouper()
            
            # 设置品牌词识别算法和n-gram分析
            use_advanced_algorithm = (brand_algorithm == "智能词组识别")
            grouper.set_brand_algorithm(use_advanced_algorithm, enable_ngram)
            
            elapsed_time = (datetime.now() - start_time).total_seconds()
            time_text.text(f"⏱️ 已用时：{elapsed_time:.1f}秒")
            
            # 显示算法信息
            if use_advanced_algorithm:
                if enable_ngram:
                    st.success("🧠 已启用：智能词组识别算法 + n-gram分析")
                else:
                    st.success("🧠 已启用：智能词组识别算法")
            else:
                st.success("⚡ 已启用：传统单词识别算法")
            
            # 1. 加载关键词文件
            status_text.markdown("**📂 正在读取关键词文件...**")
            progress_bar.progress(15)
            
            keywords = load_keywords_from_uploaded_file(keywords_file, grouper)
            if not keywords:
                st.error("❌ 无法加载关键词文件，请检查文件格式")
                return
                
            elapsed_time = (datetime.now() - start_time).total_seconds()
            time_text.text(f"⏱️ 已用时：{elapsed_time:.1f}秒")
            st.success(f"✅ 成功加载 {len(keywords)} 个关键词")
            
            # 2. 设置否定词（如果提供）
            if negative_words_list:
                status_text.markdown("**🚫 正在设置否定词...**")
                progress_bar.progress(25)
                grouper.negative_words = negative_words_list
                elapsed_time = (datetime.now() - start_time).total_seconds()
                time_text.text(f"⏱️ 已用时：{elapsed_time:.1f}秒")
                st.info(f"📝 设置否定词 {len(negative_words_list)} 个")
            
            # 3. 应用长度过滤（在清洗之前）
            original_count = len(keywords)
            if min_length is not None or max_length is not None:
                status_text.markdown("**📏 正在应用长度过滤...**")
                progress_bar.progress(35)
                keywords = apply_length_filter(keywords, min_length, max_length)
                elapsed_time = (datetime.now() - start_time).total_seconds()
                time_text.text(f"⏱️ 已用时：{elapsed_time:.1f}秒")
                if len(keywords) < original_count:
                    st.info(f"🔧 长度过滤：移除 {original_count - len(keywords)} 个关键词")
            
            # 4. 执行完整的处理流程（与keyword_grouper.py一致）
            status_text.markdown("**🧹 正在清洗关键词...**")
            progress_bar.progress(45)
            
            # 清洗关键词
            cleaned_keywords = grouper.clean_keywords(keywords)
            elapsed_time = (datetime.now() - start_time).total_seconds()
            time_text.text(f"⏱️ 已用时：{elapsed_time:.1f}秒")
            
            status_text.markdown("**🔍 正在过滤关键词...**")
            progress_bar.progress(55)
            
            # 过滤关键词
            filtered_keywords, removed_keywords = grouper.filter_keywords(cleaned_keywords)
            elapsed_time = (datetime.now() - start_time).total_seconds()
            time_text.text(f"⏱️ 已用时：{elapsed_time:.1f}秒")
            
            status_text.markdown("**🤖 正在执行智能分组...**")
            progress_bar.progress(65)
            
            # 分组（使用与keyword_grouper.py相同的方法）
            groups = grouper.group_by_common_words(filtered_keywords)
            elapsed_time = (datetime.now() - start_time).total_seconds()
            time_text.text(f"⏱️ 已用时：{elapsed_time:.1f}秒")
            
            status_text.markdown("**📊 正在生成分析结果...**")
            progress_bar.progress(75)
            
            # 5. 核心关键词推荐（如果启用）
            core_recommendation = None
            if enable_core_recommendation:
                core_recommendation = grouper.recommend_core_keyword()
            
            # 6. 获取核心品牌词
            status_text.markdown("**🎯 正在确定核心品牌词...**")
            progress_bar.progress(85)
            
            final_core_brand = ""
            if core_brand_option == "手动指定" and manual_core_brand.strip():
                final_core_brand = manual_core_brand.strip()
                st.success(f"📌 已手动指定核心品牌词: **{final_core_brand}**")
            else:
                # 自动识别
                final_core_brand = grouper._extract_core_brand()
                if final_core_brand:
                    st.success(f"🤖 自动识别核心品牌词: **{final_core_brand}**")
                else:
                    st.warning("⚠️ 未能自动识别出核心品牌词")

            elapsed_time = (datetime.now() - start_time).total_seconds()
            time_text.text(f"⏱️ 已用时：{elapsed_time:.1f}秒")
            
            # 构建结果（与keyword_grouper.py的输出格式一致）
            result = {
                'grouper': grouper,  # 传递完整的grouper对象
                'groups': groups,
                'filtered_keywords': removed_keywords,
                'total_keywords': len(grouper.original_keywords),
                'cleaned_count': len(cleaned_keywords),
                'filtered_count': len(removed_keywords),
                'final_count': len(filtered_keywords),
                'group_count': len(groups),
                'core_recommendation': core_recommendation,
                'core_brand': final_core_brand
            }
            
            status_text.markdown("**🎨 正在展示结果...**")
            progress_bar.progress(95)
            
            # 显示结果
            display_results(result, show_filtered)
            
            progress_bar.progress(100)
            status_text.markdown("**✅ 处理完成！**")
            elapsed_time = (datetime.now() - start_time).total_seconds()
            time_text.text(f"🎉 总用时：{elapsed_time:.1f}秒")
            
        except Exception as e:
            st.error(f"❌ 处理过程中发生错误: {str(e)}")
            with st.expander("🔍 错误详情", expanded=False):
                st.exception(e)

def load_keywords_from_uploaded_file(uploaded_file, grouper):
    """从上传的文件加载关键词"""
    try:
        # 保存上传的文件到临时位置
        temp_file_path = f"temp_{uploaded_file.name}"
        with open(temp_file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        # 使用grouper的方法加载关键词
        keywords = grouper.load_keywords_from_file(temp_file_path)
        
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
            
        return keywords
        
    except Exception as e:
        st.error(f"加载关键词文件失败: {e}")
        return []

def apply_length_filter(keywords, min_length, max_length):
    """应用长度过滤"""
    filtered_keywords = []
    for keyword in keywords:
        length = len(keyword)
        if min_length is not None and length < min_length:
            continue
        if max_length is not None and length > max_length:
            continue
        filtered_keywords.append(keyword)
    return filtered_keywords

def get_match_strategy(group_name: str) -> tuple:
    """
    根据分组类型推荐匹配方式和出价策略
    
    Args:
        group_name: 分组名称
        
    Returns:
        (匹配方式, 出价策略)
    """
    strategies = {
        '核心品牌词组': ('精确匹配', '最高出价'),
        '域名词': ('精确匹配', '高出价'),
        '下载词': ('广泛匹配', '中等出价'),
        '疑问词': ('短语匹配', '低出价'),
        '对比词': ('精确匹配', '高出价'),
        '平台词': ('短语匹配', '中等出价'),
        '版本词': ('广泛匹配', '中等出价'),
        '功能词': ('短语匹配', '低出价')
    }
    
    return strategies.get(group_name, ('广泛匹配', '中等出价'))

def show_group_distribution_charts(result):
    """显示分组分布图表"""
    
    st.subheader("📈 分组分布分析")
    
    groups = result['groups']
    if not groups:
        st.warning("没有分组数据可以显示")
        return
    
    # 准备图表数据
    group_names = list(groups.keys())
    group_sizes = [len(keywords) for keywords in groups.values()]
    
    # 创建两列布局
    col1, col2 = st.columns(2)
    
    with col1:
        # 饼图 - 分组关键词数量分布
        fig_pie = px.pie(
            values=group_sizes,
            names=group_names,
            title="分组关键词数量分布",
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        fig_pie.update_traces(textposition='inside', textinfo='percent+label')
        fig_pie.update_layout(
            showlegend=True,
            legend=dict(
                orientation="v",
                yanchor="middle",
                y=0.5,
                xanchor="left",
                x=1.05
            ),
            width=400,
            height=400
        )
        st.plotly_chart(fig_pie, use_container_width=True)
    
    with col2:
        # 柱状图 - 各分组关键词数量
        fig_bar = px.bar(
            x=group_names,
            y=group_sizes,
            title="各分组关键词数量",
            labels={'x': '分组名称', 'y': '关键词数量'},
            color=group_sizes,
            color_continuous_scale='Blues'
        )
        fig_bar.update_layout(
            xaxis_tickangle=-45,
            width=400,
            height=400,
            showlegend=False
        )
        # 在柱子上显示数值
        fig_bar.update_traces(texttemplate='%{y}', textposition='outside')
        st.plotly_chart(fig_bar, use_container_width=True)
    
    # 详细统计表格
    st.subheader("📊 分组统计详情")
    
    # 计算统计数据
    total_keywords = sum(group_sizes)
    stats_data = []
    
    for group_name, keywords in groups.items():
        keyword_count = len(keywords)
        percentage = round((keyword_count / total_keywords) * 100, 1)
        
        # 计算平均关键词长度
        avg_length = round(sum(len(kw) for kw in keywords) / len(keywords), 1) if keywords else 0
        
        stats_data.append({
            '分组名称': group_name,
            '关键词数量': keyword_count,
            '占比(%)': percentage,
            '平均长度': avg_length,
            '示例关键词': ', '.join(keywords[:3]) + ('...' if len(keywords) > 3 else '')
        })
    
    # 按关键词数量排序
    stats_data.sort(key=lambda x: x['关键词数量'], reverse=True)
    
    # 显示统计表格
    stats_df = pd.DataFrame(stats_data)
    st.dataframe(stats_df, use_container_width=True)
    
    # 总体统计
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总分组数", len(groups))
    
    with col2:
        st.metric("总关键词数", total_keywords)
    
    with col3:
        largest_group = max(groups.items(), key=lambda x: len(x[1]))
        st.metric("最大分组", f"{largest_group[0]}", f"{len(largest_group[1])}个")
    
    with col4:
        avg_group_size = round(total_keywords / len(groups), 1)
        st.metric("平均分组大小", f"{avg_group_size}个")

def display_results(result, show_filtered):
    """显示分组结果"""
    
    st.markdown("---")
    st.header("📊 分组结果")
    
    # 统计信息
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总关键词数", result['total_keywords'])
    
    with col2:
        st.metric("清洗后数量", result['cleaned_count'])
    
    with col3:
        st.metric("最终数量", result['final_count'])
    
    with col4:
        st.metric("分组数量", result['group_count'])
    
    # 核心品牌词信息
    if result.get('core_brand'):
        st.info(f"🎯 核心品牌词: **{result['core_brand']}**")
        
        # 显示品牌词分析过程（调试信息）
        grouper = result.get('grouper')
        if grouper and hasattr(grouper, 'brand_analysis_debug'):
            with st.expander("🔍 品牌词分析过程", expanded=False):
                debug_info = grouper.brand_analysis_debug
                
                if debug_info.get('algorithm') in ['advanced', 'advanced_with_ngram']:
                    if debug_info.get('ngram_enabled'):
                        st.write("**🧠 智能词组识别算法 + n-gram分析：**")
                    else:
                        st.write("**🧠 智能词组识别算法分析：**")
                    
                    candidates = debug_info.get('candidates', {})
                    scores = debug_info.get('scores', {})
                    
                    if candidates:
                        candidate_count = len(candidates)
                        if debug_info.get('ngram_enabled'):
                            st.write(f"📊 发现 {candidate_count} 个候选词组（包含n-gram分析结果）：")
                        else:
                            st.write(f"📊 发现 {candidate_count} 个候选词组：")
                        
                        # 显示候选词组和得分
                        analysis_data = []
                        for phrase, freq in candidates.items():
                            score = scores.get(phrase, 0)
                            coverage = sum(1 for kw in grouper.filtered_keywords if phrase in kw)
                            coverage_rate = round(coverage / len(grouper.filtered_keywords) * 100, 1)
                            
                            analysis_data.append({
                                '候选词组': phrase,
                                '出现频率': freq,
                                '品牌得分': round(score, 2),
                                '覆盖率': f"{coverage_rate}%"
                            })
                        
                        # 按得分排序
                        analysis_data.sort(key=lambda x: x['品牌得分'], reverse=True)
                        
                        analysis_df = pd.DataFrame(analysis_data)
                        st.dataframe(analysis_df, use_container_width=True)
                        
                        if debug_info.get('fallback_used'):
                            st.warning("⚠️ 未找到合适的词组，使用单词分析作为备选")
                    else:
                        st.warning("未找到候选词组")
                        
                elif debug_info.get('algorithm') == 'legacy':
                    st.write("**⚡ 传统单词识别算法分析：**")
                    
                    word_counter = debug_info.get('word_counter', {})
                    if word_counter:
                        st.write("📊 高频词统计（前10个）：")
                        
                        legacy_data = []
                        for word, freq in word_counter.items():
                            coverage = sum(1 for kw in grouper.filtered_keywords if word in kw)
                            coverage_rate = round(coverage / len(grouper.filtered_keywords) * 100, 1)
                            
                            legacy_data.append({
                                '词根': word,
                                '出现频率': freq,
                                '覆盖率': f"{coverage_rate}%"
                            })
                        
                        legacy_df = pd.DataFrame(legacy_data)
                        st.dataframe(legacy_df, use_container_width=True)
                
                st.success(f"✅ 最终选择: **{debug_info.get('result', '未知')}**")
    
    # 核心关键词推荐
    if result.get('core_recommendation'):
        st.subheader("💡 核心关键词推荐")
        st.write(f"推荐的核心关键词: **{result['core_recommendation']}**")
    
    # 显示被过滤的关键词 - 优化显示方式
    if show_filtered and result['filtered_keywords']:
        st.subheader("🚫 被过滤的关键词")
        filtered_count = len(result['filtered_keywords'])
        
        # 创建两列布局
        col1, col2 = st.columns([3, 1])
        
        with col1:
            with st.expander(f"查看被过滤的 {filtered_count} 个关键词", expanded=False):
                # 将过滤的关键词分成多列显示，更紧凑
                filtered_keywords = result['filtered_keywords']
                
                # 每页显示50个关键词
                page_size = 50
                total_pages = (len(filtered_keywords) + page_size - 1) // page_size
                
                if total_pages > 1:
                    page = st.selectbox("选择页面", range(1, total_pages + 1), key="filtered_page")
                    start_idx = (page - 1) * page_size
                    end_idx = min(start_idx + page_size, len(filtered_keywords))
                    current_keywords = filtered_keywords[start_idx:end_idx]
                    st.info(f"显示第 {page} 页，共 {total_pages} 页（第 {start_idx + 1}-{end_idx} 个关键词）")
                else:
                    current_keywords = filtered_keywords
                
                # 分4列显示关键词
                cols = st.columns(4)
                for i, keyword in enumerate(current_keywords):
                    with cols[i % 4]:
                        st.write(f"• {keyword}")
        
        with col2:
            st.metric("过滤数量", filtered_count)
            if filtered_count > 0:
                filter_rate = round((filtered_count / result['total_keywords']) * 100, 1)
                st.metric("过滤率", f"{filter_rate}%")
    
    # 显示分组分布图表
    show_group_distribution_charts(result)
    
    # 显示分组结果
    st.subheader("📋 详细分组")
    
    groups = result['groups']
    
    # 为每个分组创建一个可展开的区域
    for group_name, keywords in groups.items():
        with st.expander(f"**{group_name}** ({len(keywords)} 个关键词)", expanded=False):
            # 分列显示关键词
            cols = st.columns(3)
            for i, keyword in enumerate(keywords):
                with cols[i % 3]:
                    st.write(f"• {keyword}")
    
    # 导出功能
    st.subheader("📥 导出数据")
    export_data(result)

def export_data(result):
    """导出数据功能 - 与keyword_grouper.py格式一致"""
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**📊 完整分组数据**")
        
        # 按照keyword_grouper.py的格式准备完整分组数据
        complete_export_rows = []
        groups = result['groups']
        core_brand = result.get('core_brand', '软件')
        campaign_name = f"第三方软件-{core_brand}" if core_brand else "第三方软件"
        
        # 首先添加核心品牌词组（如果存在）
        if core_brand:
            complete_export_rows.append({
                'Campaign': campaign_name,
                'Ad Group': '核心品牌词组',
                'Keyword': core_brand,
                'Original Keyword': core_brand
            })
        
        # 添加其他分组的所有关键词
        for group_name, keywords in groups.items():
            for keyword in keywords:
                # 跳过核心品牌词，避免重复
                if core_brand and keyword.lower() == core_brand.lower():
                    continue
                    
                # 找到对应的原始关键词
                original_keyword = ""
                grouper = result.get('grouper')
                if grouper and grouper.original_keywords:
                    for orig in grouper.original_keywords:
                        if orig.strip().lower() == keyword:
                            original_keyword = orig
                            break
                
                complete_export_rows.append({
                    'Campaign': campaign_name,
                    'Ad Group': group_name,
                    'Keyword': keyword,
                    'Original Keyword': original_keyword or keyword
                })
        
        if complete_export_rows:
            # 创建DataFrame
            complete_df = pd.DataFrame(complete_export_rows)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            complete_filename = f"keywords_{timestamp}_完整分组.csv"
            
            # 转换为CSV
            complete_csv = complete_df.to_csv(index=False, encoding='utf-8-sig')
            
            # 下载按钮 - 添加key参数避免页面重新加载
            st.download_button(
                label="📊 下载完整分组数据",
                data=complete_csv,
                file_name=complete_filename,
                mime="text/csv",
                use_container_width=True,
                key="download_complete_data"
            )
            
            # 预览 - 只显示推广计划、推广组、关键词三列
            st.write("**📋 完整分组预览：**")
            if complete_export_rows:
                # 创建预览数据，只包含指定的三列
                preview_data = []
                for row in complete_export_rows:
                    preview_data.append({
                        '推广计划': row['Campaign'],
                        '推广组': row['Ad Group'],
                        '关键词': row['Keyword']
                    })
                
                complete_preview_df = pd.DataFrame(preview_data)
                # 显示前10行，只显示三列
                st.dataframe(
                    complete_preview_df.head(10),
                    use_container_width=True,
                    column_config={
                        '推广计划': st.column_config.TextColumn('推广计划', width='medium'),
                        '推广组': st.column_config.TextColumn('推广组', width='medium'), 
                        '关键词': st.column_config.TextColumn('关键词', width='medium')
                    }
                )
                st.info(f"完整分组包含 {len(complete_export_rows)} 个关键词")
    
    with col2:
        st.write("**🎯 推广计划数据（去重版本）**")
        
        # 按照keyword_grouper.py的格式准备推广计划数据（去重版本）
        plan_export_rows = []
        groups = result['groups']
        core_brand = result.get('core_brand', '软件')
        campaign_name = f"第三方软件-{core_brand}" if core_brand else "第三方软件"
        
        # 核心品牌词组
        if core_brand:
            plan_export_rows.append({
                'Campaign': campaign_name,
                'Ad Group': '核心品牌词组',
                'Keyword': core_brand,
                'Original Keyword': core_brand,
                'Match Type': '精确匹配',
                'Bid Strategy': '最高出价',
                'Keywords Count': 1
            })
        
        # 每个分组只选择一个代表性关键词
        for group_name, group_keywords in groups.items():
            if not group_keywords:
                continue
            
            # 选择代表性关键词的策略：
            # 1. 优先选择最短的关键词（通常更核心）
            # 2. 如果长度相同，选择字母序最小的
            filtered_keywords = [kw for kw in group_keywords 
                               if not (core_brand and kw.lower() == core_brand.lower())]
            
            if not filtered_keywords:
                continue
            
            representative_keyword = min(filtered_keywords, 
                                       key=lambda x: (len(x), x.lower()))
            
            # 找到对应的原始关键词
            original_keyword = ""
            grouper = result.get('grouper')
            if grouper and grouper.original_keywords:
                for orig in grouper.original_keywords:
                    if orig.strip().lower() == representative_keyword:
                        original_keyword = orig
                        break
            
            # 根据分组类型推荐匹配方式和出价策略
            match_type, bid_strategy = get_match_strategy(group_name)
            
            plan_export_rows.append({
                'Campaign': campaign_name,
                'Ad Group': group_name,
                'Keyword': representative_keyword,
                'Original Keyword': original_keyword or representative_keyword,
                'Match Type': match_type,
                'Bid Strategy': bid_strategy,
                'Keywords Count': len(filtered_keywords)
            })
        
        if plan_export_rows:
            # 创建DataFrame
            plan_df = pd.DataFrame(plan_export_rows)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plan_filename = f"keywords_{timestamp}_推广计划.csv"
            
            # 转换为CSV
            plan_csv = plan_df.to_csv(index=False, encoding='utf-8-sig')
            
            # 下载按钮 - 添加key参数避免页面重新加载
            st.download_button(
                label="🎯 下载推广计划数据",
                data=plan_csv,
                file_name=plan_filename,
                mime="text/csv",
                use_container_width=True,
                key="download_plan_data"
            )
            
            # 预览 - 只显示推广计划和推广组两列
            st.write("**🎯 推广计划预览：**")
            if plan_export_rows:
                # 创建预览数据，只包含推广计划和推广组两列
                preview_data = []
                for row in plan_export_rows:
                    preview_data.append({
                        '推广计划': row['Campaign'],
                        '推广组': row['Ad Group']
                    })
                
                plan_preview_df = pd.DataFrame(preview_data)
                # 显示前10行，只显示两列
                st.dataframe(
                    plan_preview_df.head(10),
                    use_container_width=True,
                    column_config={
                        '推广计划': st.column_config.TextColumn('推广计划', width='medium'),
                        '推广组': st.column_config.TextColumn('推广组', width='medium')
                    }
                )
                st.info(f"推广计划包含 {len(plan_export_rows)} 个广告组")
    
    # 下载按钮
    st.markdown("---")
    st.info("💡 **提示：** 完整分组数据包含所有关键词，推广计划数据每个分组只保留一个代表性关键词")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        st.error(f"应用启动失败: {str(e)}")
        st.exception(e)