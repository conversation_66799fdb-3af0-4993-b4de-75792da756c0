"""
FastAPI版本的SEM关键词智能分组工具
现代化的Web应用，专注于用户体验和性能
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import JSONResponse, FileResponse, Response
import asyncio
import os
import json
import tempfile
import shutil
import io
from datetime import datetime
from typing import List, Optional, Dict, Any
import uuid
import pandas as pd
from keyword_grouper import KeywordGrouper
from baidu_to_bing_converter import BaiduToBingConverter
from keyword_expander import KeywordExpander
from keyword_id_syncer import KeywordIdSyncer
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="SEM关键词智能分组工具",
    description="现代化的关键词分组和管理工具",
    version="2.0.0"
)

# 创建必要的目录
os.makedirs("static", exist_ok=True)
os.makedirs("templates", exist_ok=True)
os.makedirs("uploads", exist_ok=True)
os.makedirs("exports", exist_ok=True)

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'port': 23006,
    'user': 'db_read',
    'password': 'LO5ku9RvVS8BsT1m',
    'database': 'duba_pkg_mgr',
    'charset': 'utf8mb4'
}

# 全局变量存储处理状态
processing_status = {}

class ProcessingStatus:
    def __init__(self):
        self.current_step = ""
        self.progress = 0
        self.message = ""
        self.completed = False
        self.error = None
        self.result = None
        
    def update(self, step: str, progress: int, message: str):
        self.current_step = step
        self.progress = progress
        self.message = message
        logger.info(f"Processing update: {step} - {progress}% - {message}")
        
    def complete(self, result: Any):
        self.completed = True
        self.progress = 100
        self.result = result
        self.message = "处理完成"
        logger.info("Processing completed successfully")
        
    def error(self, error_message: str):
        self.error = error_message
        self.completed = True
        self.progress = 0
        logger.error(f"Processing error: {error_message}")

@app.get("/")
async def home(request: Request):
    """首页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/ad-tips")
async def prompt_display(request: Request):
    """广告创意生成"""
    return templates.TemplateResponse("prompt_display.html", {"request": request})

@app.post("/api/upload-keywords")
async def upload_keywords(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    negative_words: str = Form(""),
    brand_algorithm: str = Form("智能词组识别"),
    enable_ngram: bool = Form(True),
    show_filtered: bool = Form(True),
    enable_core_recommendation: bool = Form(True),
    core_brand_option: str = Form("自动识别"),
    manual_core_brand: str = Form("")
):
    """上传关键词文件并开始处理"""
    
    # 生成唯一的任务ID
    task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 验证文件类型
    if not file.filename.lower().endswith(('.txt', '.csv', '.xlsx')):
        raise HTTPException(status_code=400, detail="不支持的文件类型，请上传TXT、CSV或XLSX文件")
    
    # 验证文件大小 (200MB)
    if file.size > 200 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="文件过大，请上传小于200MB的文件")
    
    # 初始化处理状态
    processing_status[task_id] = ProcessingStatus()
    
    try:
        # 保存上传的文件
        upload_path = os.path.join("uploads", f"{task_id}_{file.filename}")
        with open(upload_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 准备处理参数
        process_params = {
            "task_id": task_id,
            "file_path": upload_path,
            "negative_words": negative_words,
            "brand_algorithm": brand_algorithm,
            "enable_ngram": enable_ngram,
            "show_filtered": show_filtered,
            "enable_core_recommendation": enable_core_recommendation,
            "core_brand_option": core_brand_option,
            "manual_core_brand": manual_core_brand
        }
        
        # 启动后台处理任务
        background_tasks.add_task(process_keywords_background, **process_params)
        
        return JSONResponse({
            "success": True,
            "task_id": task_id,
            "message": "文件上传成功，正在处理中...",
            "filename": file.filename,
            "file_size": file.size
        })
        
    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        if task_id in processing_status:
            processing_status[task_id].error(str(e))
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

async def process_keywords_background(
    task_id: str,
    file_path: str,
    negative_words: str,
    brand_algorithm: str,
    enable_ngram: bool,
    show_filtered: bool,
    enable_core_recommendation: bool,
    core_brand_option: str,
    manual_core_brand: str
):
    """后台处理关键词分组任务"""
    
    status = processing_status[task_id]
    
    try:
        # 1. 初始化分组器
        status.update("初始化", 5, "正在初始化分组器...")
        await asyncio.sleep(0.1)  # 让UI有时间更新
        
        grouper = KeywordGrouper()
        use_advanced_algorithm = (brand_algorithm == "智能词组识别")
        grouper.set_brand_algorithm(use_advanced_algorithm, enable_ngram)
        
        # 2. 加载关键词文件
        status.update("加载文件", 15, "正在读取关键词文件...")
        await asyncio.sleep(0.1)
        
        keywords = grouper.load_keywords_from_file(file_path)
        if not keywords:
            raise Exception("无法加载关键词文件，请检查文件格式")
        
        # 3. 设置否定词
        if negative_words.strip():
            status.update("设置否定词", 25, "正在设置否定词...")
            await asyncio.sleep(0.1)
            negative_words_list = [word.strip() for word in negative_words.split('\n') if word.strip()]
            grouper.negative_words = negative_words_list
        
        # 4. 清洗关键词
        status.update("清洗关键词", 35, "正在清洗关键词...")
        await asyncio.sleep(0.1)
        
        cleaned_keywords = grouper.clean_keywords(keywords)
        
        # 5. 过滤关键词
        status.update("过滤关键词", 50, "正在过滤关键词...")
        await asyncio.sleep(0.1)
        
        filtered_keywords, removed_keywords = grouper.filter_keywords(cleaned_keywords)
        
        # 6. 执行分组
        status.update("智能分组", 70, "正在执行智能分组...")
        await asyncio.sleep(0.1)
        
        groups = grouper.group_by_common_words(filtered_keywords)
        
        # 7. 核心关键词推荐
        core_recommendation = None
        if enable_core_recommendation:
            status.update("核心词推荐", 85, "正在推荐核心关键词...")
            await asyncio.sleep(0.1)
            core_recommendation = grouper.recommend_core_keyword()
        
        # 8. 确定核心品牌词
        status.update("品牌词识别", 90, "正在确定核心品牌词...")
        await asyncio.sleep(0.1)
        
        final_core_brand = ""
        if core_brand_option == "手动指定" and manual_core_brand.strip():
            final_core_brand = manual_core_brand.strip()
        else:
            final_core_brand = grouper._extract_core_brand()
        
        # 9. 构建结果
        status.update("生成结果", 95, "正在生成分析结果...")
        await asyncio.sleep(0.1)
        
        result = {
            'groups': groups,
            'filtered_keywords': removed_keywords,
            'total_keywords': len(grouper.original_keywords),
            'cleaned_count': len(cleaned_keywords),
            'filtered_count': len(removed_keywords),
            'final_count': len(filtered_keywords),
            'group_count': len(groups),
            'core_recommendation': core_recommendation,
            'core_brand': final_core_brand,
            'brand_algorithm': brand_algorithm,
            'enable_ngram': enable_ngram,
            'show_filtered': show_filtered,
            'processing_time': datetime.now().isoformat()
        }
        
        # 10. 完成处理
        status.complete(result)
        
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)
            
    except Exception as e:
        logger.error(f"Processing error for task {task_id}: {str(e)}")
        status.error(str(e))
        
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)

@app.get("/api/status/{task_id}")
async def get_processing_status(task_id: str):
    """获取处理状态"""
    
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    status = processing_status[task_id]
    
    # 确定任务状态
    if status.error:
        task_status = "failed"
    elif status.completed:
        task_status = "completed"
    else:
        task_status = "processing"
    
    # 返回平面结构，方便前端解析
    return JSONResponse({
        "success": True,
        "task_id": task_id,
        "current_step": status.current_step,
        "progress": status.progress,
        "message": status.message,
        "status": task_status,
        "completed": status.completed,
        "error": status.error,
        "result": status.result
    })

@app.get("/api/processing-status/{task_id}")
async def get_processing_status_alt(task_id: str):
    """获取处理状态（备用路径）"""
    return await get_processing_status(task_id)

@app.get("/api/result/{task_id}")
async def get_result(task_id: str):
    """获取处理结果"""
    
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    status = processing_status[task_id]
    
    if status.error:
        raise HTTPException(status_code=500, detail=status.error)
    
    if not status.completed or not status.result:
        raise HTTPException(status_code=202, detail="任务仍在处理中")
    
    return JSONResponse({
        "success": True,
        "task_id": task_id,
        "result": status.result
    })

@app.post("/api/export/{task_id}")
async def export_result(
    task_id: str,
    export_type: str = Form("complete")  # "complete" or "campaign"
):
    """导出结果"""
    
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    status = processing_status[task_id]
    
    if not status.completed or not status.result:
        raise HTTPException(status_code=400, detail="任务未完成或无结果")
    
    try:
        result = status.result
        groups = result['groups']
        core_brand = result.get('core_brand', '软件')
        campaign_name = f"第三方软件-{core_brand}" if core_brand else "第三方软件"
        
        export_rows = []
        
        if export_type == "complete":
            # 完整分组数据
            
            # 首先添加核心品牌词组
            if core_brand:
                export_rows.append({
                    'Campaign': campaign_name,
                    'Ad Group': '核心品牌词组',
                    'Keyword': core_brand,
                    'Original Keyword': core_brand
                })
            
            # 添加其他分组的所有关键词
            for group_name, keywords in groups.items():
                for keyword in keywords:
                    if core_brand and keyword.lower() == core_brand.lower():
                        continue
                    
                    export_rows.append({
                        'Campaign': campaign_name,
                        'Ad Group': group_name,
                        'Keyword': keyword,
                        'Original Keyword': keyword
                    })
            
            filename = f"keywords_{task_id}_完整分组.csv"
            
        else:  # campaign
            # 推广计划数据（去重版本）
            
            # 核心品牌词组
            if core_brand:
                export_rows.append({
                    'Campaign': campaign_name,
                    'Ad Group': '核心品牌词组',
                    'Keyword': core_brand,
                    'Match Type': '精确匹配',
                    'Bid Strategy': '最高出价',
                    'Keywords Count': 1
                })
            
            # 每个分组只选择一个代表性关键词
            for group_name, group_keywords in groups.items():
                if not group_keywords:
                    continue
                
                filtered_keywords = [kw for kw in group_keywords 
                                   if not (core_brand and kw.lower() == core_brand.lower())]
                
                if not filtered_keywords:
                    continue
                
                representative_keyword = min(filtered_keywords, 
                                           key=lambda x: (len(x), x.lower()))
                
                # 根据分组类型推荐匹配方式和出价策略
                strategies = {
                    '核心品牌词组': ('精确匹配', '最高出价'),
                    '域名词': ('精确匹配', '高出价'),
                    '下载词': ('广泛匹配', '中等出价'),
                    '疑问词': ('短语匹配', '低出价'),
                    '对比词': ('精确匹配', '高出价'),
                    '平台词': ('短语匹配', '中等出价'),
                    '版本词': ('广泛匹配', '中等出价'),
                    '功能词': ('短语匹配', '低出价')
                }
                
                match_type, bid_strategy = strategies.get(group_name, ('广泛匹配', '中等出价'))
                
                export_rows.append({
                    'Campaign': campaign_name,
                    'Ad Group': group_name,
                    'Keyword': representative_keyword,
                    'Match Type': match_type,
                    'Bid Strategy': bid_strategy,
                    'Keywords Count': len(filtered_keywords)
                })
            
            filename = f"keywords_{task_id}_推广计划.csv"
        
        # 创建DataFrame并保存
        df = pd.DataFrame(export_rows)
        export_path = os.path.join("exports", filename)
        df.to_csv(export_path, index=False, encoding='utf-8-sig')
        
        return JSONResponse({
            "success": True,
            "filename": filename,
            "download_url": f"/api/download/{filename}",
            "rows_count": len(export_rows)
        })
        
    except Exception as e:
        logger.error(f"Export error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/api/download/{filename}")
async def download_file(filename: str):
    """下载导出的文件"""
    
    file_path = os.path.join("exports", filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")
    
    return FileResponse(
        file_path,
        media_type='application/octet-stream',
        filename=filename
    )

@app.get("/api/negative-words/default")
async def get_default_negative_words():
    """获取默认否定词"""
    
    try:
        negative_words_file = "keyword/negative_words.txt"
        if os.path.exists(negative_words_file):
            with open(negative_words_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                return JSONResponse({
                    "success": True,
                    "negative_words": content
                })
        else:
            # 默认否定词
            default_words = [
                "招聘", "论坛", "破解", "手机", "app", "安卓", "ios", "iphone", "android",
                "苹果", "移动", "电话", "游戏", "小说", "视频",
                "图片", "壁纸", "头像", "表情", "emoji", "聊天", "社交", "交友",
                "直播", "短视频", "抖音", "快手", "微博"
            ]
            return JSONResponse({
                "success": True,
                "negative_words": '\n'.join(default_words)
            })
    except Exception as e:
        logger.error(f"Get default negative words error: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": str(e)
        })

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("FastAPI应用启动成功")
    
    # 清理旧的上传文件和导出文件
    for directory in ["uploads", "exports"]:
        if os.path.exists(directory):
            for file in os.listdir(directory):
                file_path = os.path.join(directory, file)
                if os.path.isfile(file_path):
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        logger.warning(f"清理文件失败: {file_path}, {str(e)}")

# ==================== 百度转Bing功能 API ====================

@app.post("/api/upload-baidu-file")
async def upload_baidu_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    enable_url_parsing: bool = Form(True),
    enable_data_cleaning: bool = Form(True),
    preserve_original_data: bool = Form(False)
):
    """上传百度SEM文件并开始转换"""
    
    # 生成唯一的任务ID
    task_id = f"baidu_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 验证文件类型
    if not file.filename.lower().endswith(('.csv', '.xlsx')):
        raise HTTPException(status_code=400, detail="不支持的文件类型，请上传CSV或XLSX文件")
    
    # 验证文件大小 (200MB)
    if file.size > 200 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="文件过大，请上传小于200MB的文件")
    
    # 初始化处理状态
    processing_status[task_id] = ProcessingStatus()
    
    try:
        # 保存上传的文件
        upload_path = os.path.join("uploads", f"{task_id}_{file.filename}")
        with open(upload_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 准备处理参数
        convert_params = {
            "task_id": task_id,
            "file_path": upload_path,
            "enable_url_parsing": enable_url_parsing,
            "enable_data_cleaning": enable_data_cleaning,
            "preserve_original_data": preserve_original_data
        }
        
        # 启动后台转换任务
        background_tasks.add_task(convert_baidu_to_bing_background, **convert_params)
        
        return JSONResponse({
            "success": True,
            "task_id": task_id,
            "message": "文件上传成功，正在转换中...",
            "filename": file.filename,
            "file_size": file.size
        })
        
    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        if task_id in processing_status:
            processing_status[task_id].error(str(e))
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

async def convert_baidu_to_bing_background(
    task_id: str,
    file_path: str,
    enable_url_parsing: bool,
    enable_data_cleaning: bool,
    preserve_original_data: bool
):
    """后台执行百度转Bing转换任务"""
    
    status = processing_status[task_id]
    
    try:
        # 1. 初始化转换器
        status.update("初始化", 10, "正在初始化转换器...")
        await asyncio.sleep(0.1)
        
        converter = BaiduToBingConverter()
        
        # 2. 读取文件
        status.update("读取文件", 20, "正在读取百度SEM文件...")
        await asyncio.sleep(0.1)
        
        df = converter._read_file_with_encoding_fallback(file_path)
        if df.empty:
            raise Exception("文件为空或格式不正确")
        
        # 3. 文件预处理
        status.update("预处理", 40, "正在进行数据预处理...")
        await asyncio.sleep(0.1)
        
        # 数据清洗
        if enable_data_cleaning:
            original_count = len(df)
            df = df.dropna(how='all')  # 删除完全空白的行
            df = df.drop_duplicates()  # 去重
            cleaned_count = len(df)
            logger.info(f"数据清洗: {original_count} -> {cleaned_count}")
        
        # 4. 执行转换
        status.update("转换数据", 60, "正在转换为Bing Ads格式...")
        await asyncio.sleep(0.1)
        
        # 使用转换器的核心转换逻辑
        result_df = converter.create_new_dataframe(df)
        
        if result_df is None or result_df.empty:
            raise Exception("转换失败，未生成有效数据")
        
        # 如果不启用URL解析，清空URL列
        if not enable_url_parsing and 'Final Url' in result_df.columns:
            result_df['Final Url'] = ""
        
        # 如果保留原始数据，添加原始列
        if preserve_original_data:
            original_cols_to_keep = ['关键词名称', '推广计划名称', '推广单元名称', '匹配模式', '出价']
            for col in original_cols_to_keep:
                if col in df.columns:
                    result_df[f'原始_{col}'] = df[col]
        
        # 按模板调整列结构
        result_df = converter.align_with_template(result_df)
        
        # 5. 后处理
        status.update("后处理", 80, "正在进行后处理...")
        await asyncio.sleep(0.1)
        
        # 最终数据清洗
        if enable_data_cleaning:
            result_df = result_df.dropna(subset=['Keyword'])  # 确保关键词不为空
        
        # 6. 保存结果
        status.update("保存结果", 95, "正在保存转换结果...")
        await asyncio.sleep(0.1)
        
        # 构建结果
        result = {
            'result_df': result_df.to_dict('records'),
            'columns': result_df.columns.tolist(),
            'total_rows': len(result_df),
            'original_rows': len(df) if 'df' in locals() else 0,
            'conversion_time': datetime.now().isoformat(),
            'settings': {
                'enable_url_parsing': enable_url_parsing,
                'enable_data_cleaning': enable_data_cleaning,
                'preserve_original_data': preserve_original_data
            }
        }
        
        # 7. 完成转换
        status.complete(result)
        
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)
            
    except Exception as e:
        logger.error(f"Conversion error for task {task_id}: {str(e)}")
        status.error(str(e))
        
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)

@app.post("/api/preview-baidu-file")
async def preview_baidu_file(file: UploadFile = File(...)):
    """预览百度SEM文件内容"""
    
    try:
        # 保存临时文件
        temp_file_path = f"temp_preview_{file.filename}"
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 创建转换器并读取文件
        converter = BaiduToBingConverter()
        preview_df = converter._read_file_with_encoding_fallback(temp_file_path)
        
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        
        if preview_df.empty:
            return JSONResponse({
                "success": False,
                "error": "文件为空或格式不正确"
            })
        
        # 返回预览数据（最多前10行）
        preview_data = preview_df.head(10).to_dict('records')
        
        return JSONResponse({
            "success": True,
            "data": preview_data,
            "columns": preview_df.columns.tolist(),
            "total_rows": len(preview_df),
            "preview_rows": len(preview_data)
        })
        
    except Exception as e:
        logger.error(f"Preview error: {str(e)}")
        # 确保清理临时文件
        temp_file_path = f"temp_preview_{file.filename}"
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        
        return JSONResponse({
            "success": False,
            "error": str(e)
        })

@app.get("/api/baidu-template-info")
async def get_baidu_template_info():
    """获取百度模板信息"""
    
    try:
        grouper = KeywordGrouper()
        template_info = grouper.get_baidu_template_info()
        
        return JSONResponse({
            "success": True,
            "template_info": template_info
        })
        
    except Exception as e:
        logger.error(f"Get template info error: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": str(e),
            "template_info": {
                "upload_template": "未知",
                "export_template": "未知"
            }
        })

@app.post("/api/export-baidu-result/{task_id}")
async def export_baidu_result(task_id: str):
    """导出百度转Bing的结果"""
    
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    status = processing_status[task_id]
    
    if not status.completed or not status.result:
        raise HTTPException(status_code=400, detail="任务未完成或无结果")
    
    try:
        result = status.result
        result_data = result['result_df']
        columns = result['columns']
        
        # 转换为DataFrame
        df = pd.DataFrame(result_data)
        
        # 确保列名正确
        if columns and len(columns) == len(df.columns):
            df.columns = columns
        
        # 生成Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Bing_Keywords')
        excel_data = output.getvalue()
        
        # 保存到exports目录
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"bing_keywords_{timestamp}.xlsx"
        export_path = os.path.join("exports", filename)
        
        with open(export_path, 'wb') as f:
            f.write(excel_data)
        
        return JSONResponse({
            "success": True,
            "filename": filename,
            "download_url": f"/api/download/{filename}",
            "rows_count": len(df)
        })
        
    except Exception as e:
        logger.error(f"Export baidu result error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

# ========== 关键词扩展相关API ==========

@app.post("/api/upload-expander-template")
async def upload_expander_template(file: UploadFile = File(...)):
    """上传关键词扩展模板文件"""
    try:
        logger.info(f"上传关键词扩展模板文件: {file.filename}")
        
        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="只支持Excel格式文件")
        
        # 验证文件大小
        content = await file.read()
        file_size = len(content)
        max_size = 200 * 1024 * 1024  # 200MB
        
        if file_size > max_size:
            raise HTTPException(status_code=400, detail="文件大小超过200MB限制")
        
        # 保存文件
        task_id = str(uuid.uuid4())
        temp_dir = os.path.join("temp", task_id)
        os.makedirs(temp_dir, exist_ok=True)
        
        file_path = os.path.join(temp_dir, file.filename)
        with open(file_path, "wb") as f:
            f.write(content)
        
        # 验证文件内容
        try:
            df = pd.read_excel(file_path)
            if 'keys' not in df.columns:
                raise ValueError("模板文件必须包含'keys'列")
            
            # 预览模板内容
            preview_data = df.head().to_dict('records')
            
            return {
                "success": True,
                "task_id": task_id,
                "file_info": {
                    "filename": file.filename,
                    "size": file_size,
                    "rows": len(df),
                    "columns": list(df.columns)
                },
                "preview": preview_data
            }
            
        except Exception as e:
            # 清理文件
            if os.path.exists(file_path):
                os.remove(file_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
            raise ValueError(f"模板文件格式错误: {str(e)}")
    
    except Exception as e:
        logger.error(f"上传模板文件失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@app.post("/api/upload-keywords-file")
async def upload_keywords_file(file: UploadFile = File(...)):
    """上传基础关键词文件"""
    try:
        logger.info(f"上传基础关键词文件: {file.filename}")
        
        # 验证文件类型
        allowed_extensions = ('.txt', '.csv', '.xlsx', '.xls')
        if not file.filename.endswith(allowed_extensions):
            raise HTTPException(status_code=400, detail="只支持TXT、CSV、Excel格式文件")
        
        # 验证文件大小
        content = await file.read()
        file_size = len(content)
        max_size = 200 * 1024 * 1024  # 200MB
        
        if file_size > max_size:
            raise HTTPException(status_code=400, detail="文件大小超过200MB限制")
        
        # 保存文件
        task_id = str(uuid.uuid4())
        temp_dir = os.path.join("temp", task_id)
        os.makedirs(temp_dir, exist_ok=True)
        
        file_path = os.path.join(temp_dir, file.filename)
        with open(file_path, "wb") as f:
            f.write(content)
        
        # 读取关键词
        try:
            keywords = []
            if file.filename.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content_str = f.read()
                keywords = [kw.strip() for kw in content_str.split('\n') if kw.strip()]
            elif file.filename.endswith('.csv'):
                df = pd.read_csv(file_path)
                keywords = df.iloc[:, 0].dropna().astype(str).tolist()
            else:  # Excel文件
                df = pd.read_excel(file_path)
                keywords = df.iloc[:, 0].dropna().astype(str).tolist()
            
            # 预览关键词
            preview_keywords = keywords[:10]
            
            return {
                "success": True,
                "task_id": task_id,
                "file_info": {
                    "filename": file.filename,
                    "size": file_size,
                    "keywords_count": len(keywords)
                },
                "keywords": keywords,
                "preview": preview_keywords
            }
            
        except Exception as e:
            # 清理文件
            if os.path.exists(file_path):
                os.remove(file_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
            raise ValueError(f"关键词文件格式错误: {str(e)}")
    
    except Exception as e:
        logger.error(f"上传关键词文件失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@app.post("/api/expand-keywords-async")
async def expand_keywords_async(request: Request, background_tasks: BackgroundTasks):
    """异步执行关键词扩展"""
    try:
        data = await request.json()
        
        keywords = data.get('keywords', [])
        campaign_name = data.get('campaign_name', '第三方软件-potplayer')
        ad_group_name = data.get('ad_group_name', '错拼词')
        match_type = data.get('match_type', 'Exact')
        bid_price = data.get('bid_price', '0.39')
        final_url = data.get('final_url', 'https://sem.duba.net/sem/dseek/f255.html?sfrom=196&TFT=16&keyID=144553')
        use_custom_template = data.get('use_custom_template', False)
        template_task_id = data.get('template_task_id')
        
        if not keywords:
            raise HTTPException(status_code=400, detail="请提供基础关键词")
        
        # 准备配置
        config = {
            "match_type": match_type,
            "bid": bid_price,
            "campaign": campaign_name,
            "ad_group": ad_group_name,
            "url": final_url,
            "keywords": keywords
        }
        
        # 设置模板文件路径
        if use_custom_template and template_task_id:
            template_dir = os.path.join("temp", template_task_id)
            if not os.path.exists(template_dir):
                raise HTTPException(status_code=400, detail="模板文件不存在")
                
            template_files = [f for f in os.listdir(template_dir) if f.endswith(('.xlsx', '.xls'))]
            
            if not template_files:
                raise HTTPException(status_code=400, detail="未找到上传的模板文件")
            
            template_path = os.path.join(template_dir, template_files[0])
            config['codigo_file'] = template_path
        else:
            # 使用默认模板
            config['codigo_file'] = 'codigo.xlsx'
            if not os.path.exists(config['codigo_file']):
                raise HTTPException(status_code=400, detail="默认模板文件不存在")
        
        # 创建后台任务
        task_id = str(uuid.uuid4())
        processing_status[task_id] = ProcessingStatus()
        
        # 启动后台任务
        background_tasks.add_task(expand_keywords_background, keywords, config, task_id)
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "关键词扩展任务已启动"
        }
    
    except Exception as e:
        logger.error(f"启动关键词扩展任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

async def expand_keywords_background(keywords: List[str], config: dict, task_id: str):
    """后台执行关键词扩展任务"""
    try:
        logger.info(f"后台扩展关键词任务开始: {task_id}")
        
        status = processing_status[task_id]
        
        # 更新任务状态
        status.update("初始化", 10, "正在初始化扩展器...")
        await asyncio.sleep(0.1)
        
        # 创建扩展器
        expander = KeywordExpander()
        
        # 更新进度
        status.update("生成关键词", 30, "正在生成扩展关键词...")
        await asyncio.sleep(0.1)
        
        # 执行扩展
        expanded_keywords = expander.process_keywords(config)
        
        if not expanded_keywords:
            status.error("未生成有效的扩展关键词")
            return
        
        # 更新进度
        status.update("创建数据表", 70, "正在创建数据表...")
        await asyncio.sleep(0.1)
        
        # 创建DataFrame
        result_df = expander.create_dataframe(expanded_keywords, config)
        
        if result_df.empty:
            status.error("数据表创建失败")
            return
        
        # 更新进度
        status.update("保存结果", 90, "正在保存结果...")
        await asyncio.sleep(0.1)
        
        # 构建结果
        result = {
            'result_df': result_df.to_dict('records'),
            'columns': result_df.columns.tolist(),
            'statistics': {
                "original_count": len(keywords),
                "expanded_count": len(expanded_keywords),
                "final_rows": len(result_df),
                "expansion_rate": round(len(expanded_keywords) / len(keywords), 1)
            },
            'preview': result_df.head(10).to_dict('records')
        }
        
        # 完成任务
        status.complete(result)
        
        logger.info(f"后台扩展关键词任务完成: {task_id}")
        
    except Exception as e:
        logger.error(f"后台扩展关键词任务失败: {task_id}, 错误: {str(e)}", exc_info=True)
        processing_status[task_id].error(f"扩展失败: {str(e)}")

@app.post("/api/export-expander-result/{task_id}")
async def export_expander_result(task_id: str):
    """导出关键词扩展结果"""
    try:
        logger.info(f"导出关键词扩展结果: {task_id}")
        
        if task_id not in processing_status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        status = processing_status[task_id]
        
        if not status.completed or not status.result:
            raise HTTPException(status_code=400, detail="任务未完成或无结果")
        
        result = status.result
        result_data = result['result_df']
        columns = result['columns']
        
        # 转换为DataFrame
        df = pd.DataFrame(result_data, columns=columns)
        
        # 生成Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Extended_Keywords')
        excel_data = output.getvalue()
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"expanded_keywords_{timestamp}.xlsx"
        
        # 直接返回文件流
        return Response(
            content=excel_data,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': f'attachment; filename="{filename}"'
            }
        )
        
    except Exception as e:
        logger.error(f"导出关键词扩展结果失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.on_event("shutdown") 
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("FastAPI应用正在关闭...")
    
    # 清理处理状态
    processing_status.clear()

# ========== 自定义关键词生成相关API ==========

@app.get("/custom-keywords")
async def custom_keywords_page(request: Request):
    """自定义关键词生成页面"""
    return templates.TemplateResponse("custom_keywords.html", {"request": request})

@app.post("/api/generate-custom-keywords")
async def generate_custom_keywords(
    background_tasks: BackgroundTasks,
    main_name: str = Form(...),
    aliases: str = Form(...),
    features: str = Form(default=""),
    enable_download_intent: bool = Form(default=True),
    enable_version_intent: bool = Form(default=True),
    enable_tech_spec_intent: bool = Form(default=True),
    enable_risk_value_intent: bool = Form(default=True),
    enable_problem_intent: bool = Form(default=True)
):
    """生成自定义关键词"""
    
    # 添加调试日志
    logger.info(f"接收到自定义关键词生成请求:")
    logger.info(f"  main_name: {main_name}")
    logger.info(f"  aliases: {aliases[:100]}...")  # 只显示前100个字符
    logger.info(f"  features: {features}")
    logger.info(f"  enable_download_intent: {enable_download_intent}")
    logger.info(f"  enable_version_intent: {enable_version_intent}")
    logger.info(f"  enable_tech_spec_intent: {enable_tech_spec_intent}")
    logger.info(f"  enable_risk_value_intent: {enable_risk_value_intent}")
    logger.info(f"  enable_problem_intent: {enable_problem_intent}")
    
    # 生成唯一的任务ID
    task_id = f"custom_keywords_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # 解析输入
        aliases_list = [alias.strip() for alias in aliases.split('\n') if alias.strip()]
        features_list = []  # 功能列表固定为空
        
        if not aliases_list:
            raise HTTPException(status_code=400, detail="请至少输入一个软件别名")
        
        # 初始化处理状态
        processing_status[task_id] = ProcessingStatus()
        
        # 准备配置参数
        config = {
            "main_name": main_name.strip(),
            "aliases": aliases_list,
            "features": features_list,
            "enable_download_intent": enable_download_intent,
            "enable_version_intent": enable_version_intent,
            "enable_tech_spec_intent": enable_tech_spec_intent,
            "enable_risk_value_intent": enable_risk_value_intent,
            "enable_problem_intent": enable_problem_intent
        }
        
        # 启动后台生成任务
        background_tasks.add_task(generate_keywords_background, task_id, config)
        
        return JSONResponse({
            "success": True,
            "task_id": task_id,
            "message": "关键词生成任务已启动",
            "config": config
        })
        
    except Exception as e:
        logger.error(f"Custom keywords generation error: {str(e)}")
        if task_id in processing_status:
            processing_status[task_id].error(str(e))
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

async def generate_keywords_background(task_id: str, config: dict):
    """后台生成关键词任务"""
    
    status = processing_status[task_id]
    
    try:
        # 1. 初始化
        status.update("初始化", 5, "正在初始化关键词生成器...")
        await asyncio.sleep(0.1)
        
        main_name = config["main_name"]
        aliases = config["aliases"]
        features = config.get("features", [])  # 安全获取features，默认为空列表
        
        # 2. 构建意图词列表
        status.update("构建意图词", 15, "正在构建意图词列表...")
        await asyncio.sleep(0.1)
        
        # 基础意图词
        download_intent = [
            "下载", "免费下载", "安全下载", "立即下载", "高速下载", "本地下载",
            "在线安装", "安装包", "安装", "离线安装包", "完整版", "安装程序",
            "下载器", "下载地址", "下载链接", "一键安装"
        ]
        
        version_intent = [
            "最新版", "新版", "最新版本", "正式版", "稳定版", "旧版本", "历史版本",
            "经典版", "怀旧版", "所有版本", "各版本"
        ]
        
        tech_spec_intent = [
            "64位", "32位", "x64", "x86", "Windows版", "Windows", "Win", 
            "Windows 11", "Windows 10", "Win11", "Win10", "Win7", "for Windows",
            "电脑专用", "PC专用"
        ]
        
        risk_value_intent = [
            "绿色版", "免安装版", "纯净版", "去广告版", "精简版", "便携版", "直装版"
        ]
        
        problem_intent = [
            "怎么用", "如何安装"
        ]
        
        # 动态生成年份
        current_year = datetime.now().year
        year_intent = [f"{year}版" for year in range(current_year, current_year - 3, -1)]
        
        # 根据配置合并意图词
        all_intent_words = []
        if config["enable_download_intent"]:
            all_intent_words.extend(download_intent)
        if config["enable_version_intent"]:
            all_intent_words.extend(version_intent + year_intent)
        if config["enable_tech_spec_intent"]:
            all_intent_words.extend(tech_spec_intent)
        if config["enable_risk_value_intent"]:
            all_intent_words.extend(risk_value_intent)
        if config["enable_problem_intent"]:
            all_intent_words.extend(problem_intent)
        
        # 3. 生成关键词
        status.update("生成关键词", 30, "正在生成关键词组合...")
        await asyncio.sleep(0.1)
        
        generated_keywords = set()
        
        # 组合模式1：[核心词] + [意图词] (两段式)
        for core in aliases:
            for intent in all_intent_words:
                generated_keywords.add(f"{core} {intent}")
                generated_keywords.add(f"{intent} {core}")
                if len(intent) <= 4:
                    generated_keywords.add(f"{core}{intent}")
        
        # 组合模式2：[核心词] + [功能] + [意图词] (三段式，超级长尾)
        if features:
            for core in aliases:
                for feature in features:
                    for intent in download_intent + ["怎么用", "如何安装"]:
                        generated_keywords.add(f"{core} {feature} {intent}")
                        generated_keywords.add(f"{core} {intent} {feature}")
        
        # 4. 清洗关键词
        status.update("清洗关键词", 60, "正在清洗关键词...")
        await asyncio.sleep(0.1)
        
        # 排除不符合的词
        exclusion_patterns = ["官方", "官网", "mac", "苹果", "ios", "安卓", "手机"]
        
        cleaned_keywords = set()
        for keyword in generated_keywords:
            # 使用正则表达式进行不区分大小写的匹配
            if not any(re.search(pattern, keyword, re.IGNORECASE) for pattern in exclusion_patterns):
                # 简单逻辑检查，避免 "微信 微信" 这样的重复
                parts = keyword.split()
                if len(parts) > 1 and len(set(parts)) == 1:
                    continue
                cleaned_keywords.add(keyword)
        
        # 5. 创建DataFrame
        status.update("创建数据表", 80, "正在创建数据表...")
        await asyncio.sleep(0.1)
        
        final_keyword_list = sorted(list(cleaned_keywords))
        df = pd.DataFrame(final_keyword_list, columns=['Keyword'])
        
        # 6. 构建结果
        status.update("生成结果", 95, "正在生成最终结果...")
        await asyncio.sleep(0.1)
        
        result = {
            'keywords': final_keyword_list,
            'total_count': len(final_keyword_list),
            'config': config,
            'statistics': {
                'aliases_count': len(aliases),
                'features_count': len(features) if features else 0,
                'intent_words_count': len(all_intent_words),
                'generated_count': len(generated_keywords),
                'cleaned_count': len(cleaned_keywords)
            },
            'preview': final_keyword_list[:20],
            'generation_time': datetime.now().isoformat()
        }
        
        # 7. 完成生成
        status.complete(result)
        
    except Exception as e:
        logger.error(f"Custom keywords generation error for task {task_id}: {str(e)}")
        status.error(str(e))

@app.post("/api/export-custom-keywords/{task_id}")
async def export_custom_keywords(task_id: str):
    """导出自定义生成的关键词"""
    
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    status = processing_status[task_id]
    
    if not status.completed or not status.result:
        raise HTTPException(status_code=400, detail="任务未完成或无结果")
    
    try:
        result = status.result
        keywords = result['keywords']
        main_name = result['config']['main_name']
        
        # 创建DataFrame
        df = pd.DataFrame(keywords, columns=['Keyword'])
        
        # 生成文件名
        filename = f"{main_name}_keywords_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        export_path = os.path.join("exports", filename)
        
        # 保存文件
        df.to_csv(export_path, index=False, encoding='utf-8-sig')
        
        return JSONResponse({
            "success": True,
            "filename": filename,
            "download_url": f"/api/download/{filename}",
            "keywords_count": len(keywords)
        })
        
    except Exception as e:
        logger.error(f"Export custom keywords error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

# ========== 关键词ID查询相关API ==========

@app.post("/api/upload-keyword-sync-file")
async def upload_keyword_sync_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    enable_import: bool = Form(False)
):
    """上传关键词文件并开始查询"""
    
    # 生成唯一的任务ID
    task_id = f"keyword_query_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 验证文件类型
    if not file.filename.lower().endswith(('.csv', '.xlsx', '.xls', '.txt')):
        raise HTTPException(status_code=400, detail="不支持的文件类型，请上传CSV、Excel或TXT文件")
    
    # 验证文件大小 (200MB)
    if file.size > 200 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="文件过大，请上传小于200MB的文件")
    
    # 初始化处理状态
    processing_status[task_id] = ProcessingStatus()
    
    try:
        # 保存上传的文件
        upload_path = os.path.join("uploads", f"{task_id}_{file.filename}")
        with open(upload_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 使用固定的数据库配置
        db_config = DB_CONFIG
        
        # 准备处理参数
        query_params = {
            "task_id": task_id,
            "file_path": upload_path,
            "db_config": db_config,
            "enable_import": enable_import
        }
        
        # 启动后台查询任务
        background_tasks.add_task(query_keywords_background, **query_params)
        
        return JSONResponse({
            "success": True,
            "task_id": task_id,
            "message": "文件上传成功，正在查询关键词...",
            "filename": file.filename,
            "file_size": file.size
        })
        
    except Exception as e:
        logger.error(f"Upload keyword query file error: {str(e)}")
        if task_id in processing_status:
            processing_status[task_id].error(str(e))
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

async def query_keywords_background(
    task_id: str,
    file_path: str,
    db_config: dict,
    enable_import: bool = False
):
    """后台执行关键词查询任务"""
    
    status = processing_status[task_id]
    
    try:
        # 1. 初始化查询器
        status.update("初始化", 5, "正在初始化关键词查询器...")
        await asyncio.sleep(0.1)
        
        syncer = KeywordIdSyncer(db_config)
        
        # 2. 加载关键词文件
        status.update("加载文件", 15, "正在读取关键词文件...")
        await asyncio.sleep(0.1)
        
        try:
            keywords_data = syncer.load_keywords_from_file(file_path)
            if not keywords_data:
                raise Exception("无法加载关键词文件，请检查文件格式")
            
            total_keywords = len(keywords_data)
            status.update("文件解析完成", 20, f"成功解析 {total_keywords} 个关键词")
            await asyncio.sleep(0.1)
            
        except Exception as file_error:
            logger.error(f"文件加载失败: {str(file_error)}")
            status.update("文件加载失败", 15, f"文件加载失败: {str(file_error)}")
            raise Exception(f"文件加载失败: {str(file_error)}")
        
        # 3. 测试数据库连接
        status.update("连接数据库", 25, "正在连接数据库...")
        await asyncio.sleep(0.1)
        
        # 测试数据库连接
        try:
            test_connection = syncer.get_db_connection()
            test_connection.close()
            status.update("数据库连接成功", 30, "数据库连接正常")
        except Exception as e:
            raise Exception(f"数据库连接失败: {str(e)}")
        
        # 4. 查询关键词
        status.update("查询关键词", 40, "正在查询关键词...")
        await asyncio.sleep(0.1)
        
        # 执行关键词查询和可选的导入
        result = syncer.process_keywords(keywords_data, enable_import, status)
        
        # 5. 生成导出数据
        status.update("生成结果", 80, "正在生成导出数据...")
        await asyncio.sleep(0.1)
        
        export_df = syncer.export_results(result)
        
        # 6. 构建最终结果
        status.update("完成查询", 95, "正在完成查询...")
        await asyncio.sleep(0.1)
        
        # 构建统计信息
        statistics = {
            'total_keywords': result['total_keywords'],
            'found_count': result['found_count'],
            'missing_count': result['missing_count'],
            'missing_campaigns_count': len(result.get('missing_campaigns', [])),
            'enable_import': enable_import
        }
        
        # 如果启用了导入功能，添加导入相关统计
        if enable_import:
            statistics.update({
                'imported_count': result.get('imported_count', 0),
                'cannot_import_count': result.get('cannot_import_count', 0)
            })
        
        final_result = {
            'query_result': result,
            'export_data': export_df.to_dict('records'),
            'export_columns': export_df.columns.tolist(),
            'statistics': statistics,
            'missing_campaigns': result.get('missing_campaigns', []),
            'processing_time': datetime.now().isoformat()
        }
        
        # 7. 完成处理
        status.complete(final_result)
        
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)
            
    except Exception as e:
        logger.error(f"Keyword query error for task {task_id}: {str(e)}")
        status.error(str(e))
        
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)

@app.post("/api/export-keyword-sync-result/{task_id}")
async def export_keyword_sync_result(task_id: str):
    """导出关键词查询结果"""
    
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    status = processing_status[task_id]
    
    if not status.completed or not status.result:
        raise HTTPException(status_code=400, detail="任务未完成或无结果")
    
    try:
        result = status.result
        export_data = result['export_data']
        columns = result['export_columns']
        
        # 转换为DataFrame
        df = pd.DataFrame(export_data, columns=columns)
        
        # 生成Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='关键词查询结果')
        excel_data = output.getvalue()
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"keyword_query_result_{timestamp}.xlsx"
        
        # 保存到exports目录
        export_path = os.path.join("exports", filename)
        with open(export_path, 'wb') as f:
            f.write(excel_data)
        
        return JSONResponse({
            "success": True,
            "filename": filename,
            "download_url": f"/api/download/{filename}",
            "rows_count": len(df),
            "statistics": result['statistics']
        })
        
    except Exception as e:
        logger.error(f"Export keyword query result error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/api/test-db-connection")
async def test_db_connection():
    """测试数据库连接"""
    
    try:
        db_config = DB_CONFIG
        
        syncer = KeywordIdSyncer(db_config)
        connection = syncer.get_db_connection()
        
        # 测试查询
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM keywords")
            result = cursor.fetchone()
            keywords_count = result['count']
        
        connection.close()
        
        return JSONResponse({
            "success": True,
            "message": "数据库连接成功",
            "keywords_count": keywords_count,
            "connection_info": {
                "host": db_config['host'],
                "port": db_config['port'],
                "database": db_config['database'],
                "user": db_config['user']
            }
        })
        
    except Exception as e:
        logger.error(f"Database connection test failed: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": str(e),
            "message": "数据库连接失败"
        })

@app.get("/keyword-sync")
async def keyword_sync_page(request: Request):
    """关键词查询页面"""
    return templates.TemplateResponse("keyword_sync.html", {"request": request})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True) 