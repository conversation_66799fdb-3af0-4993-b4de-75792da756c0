#!/usr/bin/env python3
"""
FastAPI版本的SEM关键词智能分组工具启动脚本
(已修改为兼容开发模式与服务模式)
"""
import uvicorn
import webbrowser
import socket
import time
import sys
import os
import threading

# --- [新增] 服务模式下的固定配置 ---
SERVICE_HOST = "0.0.0.0"  # [关键] 服务模式下监听所有地址
SERVICE_PORT = 8001       # [关键] 服务模式下使用固定端口，请确保此端口未被占用

def check_port_available(port, host='localhost'):
    """检查端口是否可用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        return result != 0

def find_available_port(start_port=8000, end_port=8099):
    """查找可用端口"""
    for port in range(start_port, end_port + 1):
        if check_port_available(port):
            return port
    return None

def run_in_development_mode():
    """[模式一] 作为桌面工具运行，具有交互和自动打开浏览器功能"""
    print("--- 正在以 [开发模式] 启动 ---")
    print("=" * 50)
    
    # 查找可用端口
    port = find_available_port()
    if port is None:
        print(" 无法找到可用端口 (8000-8099)")
        sys.exit(1)
    
    print(f" 找到可用端口: {port}")
    print(f" 应用地址: http://localhost:{port}")
    print("=" * 50)

    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        try:
            webbrowser.open(f'http://localhost:{port}')
            print(f" 已在浏览器中打开: http://localhost:{port}")
        except Exception as e:
            print(f" 无法自动打开浏览器: {e}")

    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动服务器（开发模式）
    uvicorn.run(
        "web_app:app",
        host="localhost", # 开发模式用localhost即可
        port=port,
        reload=True,      # 开发时开启热重载
        log_level="info"
    )

def run_in_service_mode():
    """[模式二] 作为后台服务运行，稳定、无交互"""
    print("--- 正在以 [服务模式] 启动 ---")
    
    if not check_port_available(SERVICE_PORT, SERVICE_HOST):
        print(f"[错误] 服务端口 {SERVICE_PORT} 已被占用！请检查或修改脚本中的 SERVICE_PORT。")
        sys.exit(1)
        
    print(f"服务将监听在: http://{SERVICE_HOST}:{SERVICE_PORT}")
    print(f"如果 host 是 0.0.0.0, 请通过 http://localhost:{SERVICE_PORT} 或 http://<本机IP>:{SERVICE_PORT} 访问")
    
    # 启动服务器（服务模式）
    uvicorn.run(
        "web_app:app",
        host=SERVICE_HOST,
        port=SERVICE_PORT,
        reload=False,      # [关键] 服务模式必须禁用热重载
        use_colors=False,  # [关键] 禁用彩色日志避免在非终端环境报错
        access_log=False,
        log_level="info"
    )

if __name__ == "__main__":
    # [核心修改] 通过环境变量 'APP_MODE' 来决定运行模式
    # 如果没有设置这个变量，默认进入 'development' 开发模式
    run_mode = os.environ.get('APP_MODE', 'development').lower()
    
    try:
        if run_mode == 'service':
            run_in_service_mode()
        else:
            # 任何非 'service' 的值都将进入开发模式
            run_in_development_mode()
    except Exception as e:
        print(f"发生未捕获的严重错误: {e}")
        # 在日志中记录错误堆栈，便于调试
        import traceback
        traceback.print_exc()
        sys.exit(1)