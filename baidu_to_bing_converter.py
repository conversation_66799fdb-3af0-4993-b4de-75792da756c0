# -*- coding: utf-8 -*-
"""
百度转Bing关键词转换器
支持将百度SEM关键词数据转换为Bing Ads格式
"""

import os
import pandas as pd
from urllib.parse import parse_qs, urlparse
from typing import Optional
import tempfile


class BaiduToBingConverter:
    """百度转Bing关键词转换器"""
    
    def __init__(self):
        self.upload_template_path = "codigo.xlsx"
        self.export_template_path = "1.1-关键词上传模版.xlsx"
        self.encoding_fallback = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin-1']
    
    def parse_url(self, url: str) -> str:
        """
        解析并转换URL - 修改域名和参数
        
        Args:
            url: 原始URL
            
        Returns:
            转换后的URL
        """
        try:
            if pd.isna(url) or not url or str(url).strip() == '':
                return ""
                
            parsed_url = urlparse(str(url))
            query_params = parse_qs(parsed_url.query)
            keyID = query_params.get('keyID', [''])[0]
            # 修改域名为 https://sem.duba.net，sfrom=196，TFT=16
            return f'https://sem.duba.net{parsed_url.path}?sfrom=196&TFT=16&keyID={keyID}'
        except Exception as e:
            print(f"URL解析失败: {url}, 错误: {e}")
            return str(url) if url else ""
    
    def _read_file_with_encoding_fallback(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        使用编码回退机制读取文件 - 支持CSV和Excel
        
        Args:
            file_path: 文件路径
            
        Returns:
            DataFrame或None
        """
        # 如果是Excel文件，直接读取
        if file_path.endswith(('.xlsx', '.xls')):
            try:
                df = pd.read_excel(file_path)
                print(f"成功读取Excel文件: {file_path}")
                return df
            except Exception as e:
                print(f"读取Excel文件失败: {e}")
                return None
        
        # 如果是CSV文件，使用编码回退
        for encoding in self.encoding_fallback:
            try:
                print(f"尝试使用 {encoding} 编码读取CSV文件...")
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取文件")
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取失败: {e}")
                continue
        
        print("所有编码尝试失败")
        return None
    
    def process_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理和清理DataFrame - 按照原始代码逻辑
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清理后的DataFrame
        """
        original_count = len(df)
        
        # 去除重复行
        df = df.drop_duplicates()
        
        # 去除全空行
        df = df.dropna(how='all')
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        cleaned_count = len(df)
        print(f'数据清理完成: {original_count} -> {cleaned_count} 行')
        
        return df
    
    def create_new_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建新的DataFrame - 按照原始代码逻辑
        
        Args:
            df: 百度数据DataFrame
            
        Returns:
            Bing格式DataFrame
        """
        print("正在转换数据格式...")
        
        # 检查必要的列是否存在
        required_columns = ['关键词名称', '推广计划名称', '推广单元名称', '匹配模式', '出价']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"警告：缺少必要的列: {missing_columns}")
            print(f"当前文件包含的列: {list(df.columns)}")
            # 尝试使用可能的替代列名
            column_mapping = {
                '关键词名称': ['关键词', 'keyword', 'Keyword'],
                '推广计划名称': ['推广计划', 'campaign', 'Campaign'],
                '推广单元名称': ['推广单元', '推广组', 'ad group', 'Ad Group'],
                '匹配模式': ['匹配方式', 'match type', 'Match Type'],
                '出价': ['bid', 'Bid', '价格']
            }
            
            for standard_col, alternatives in column_mapping.items():
                if standard_col not in df.columns:
                    for alt_col in alternatives:
                        if alt_col in df.columns:
                            df[standard_col] = df[alt_col]
                            print(f"使用 '{alt_col}' 作为 '{standard_col}' 的替代")
                            break
        
        # 转换匹配模式
        def convert_match_type(match_type):
            if pd.isna(match_type):
                return 'Broad'
            match_type_str = str(match_type).strip()
            if match_type_str == '精确匹配':
                return 'Exact'
            elif match_type_str == '短语匹配':
                return 'Phrase'
            else:
                return 'Broad'
        
        # 创建新的DataFrame
        new_data = {
            'Keyword': df['关键词名称'].astype(str) if '关键词名称' in df.columns else '',
            'Campaign': df['推广计划名称'].astype(str) if '推广计划名称' in df.columns else '',
            'Ad Group': df['推广单元名称'].astype(str) if '推广单元名称' in df.columns else '',
            'Match Type': df['匹配模式'].apply(convert_match_type) if '匹配模式' in df.columns else 'Broad',
            'Bid': df['出价'] if '出价' in df.columns else 0
        }
        
        # 处理最终访问网址（如果存在）- 增加"计算机端落地页链接"
        url_columns = ['计算机端落地页链接', '计算机最终访问网址', '最终访问网址', 'Final Url', 'URL', 'url']
        url_found = False
        for url_col in url_columns:
            if url_col in df.columns:
                new_data['Final Url'] = df[url_col].apply(self.parse_url)
                url_found = True
                print(f"使用 '{url_col}' 作为URL列")
                break
        
        if not url_found:
            new_data['Final Url'] = ""
        
        new_df = pd.DataFrame(new_data)
        print(f"数据转换完成，共 {len(new_df)} 行")
        
        return new_df
    
    def align_with_template(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        根据模板调整DataFrame列结构
        
        Args:
            df: 数据DataFrame
            
        Returns:
            调整后的DataFrame
        """
        try:
            template_path = self.export_template_path
            if os.path.exists(template_path):
                template_df = pd.read_excel(template_path)
                template_columns = template_df.columns.tolist()
                
                # 确保所有模板列都存在
                for col in template_columns:
                    if col not in df.columns:
                        df[col] = ""
                
                # 重新排列列顺序
                df = df.reindex(columns=template_columns)
                print(f"已按模板调整列结构")
            else:
                print(f"模板文件不存在: {template_path}")
            
            return df
            
        except Exception as e:
            print(f"模板对齐失败: {e}")
            return df
    
    def convert_file(self, input_file_path: str, output_file_path: str = None) -> bool:
        """
        转换文件 - 按照原始代码的main函数逻辑
        
        Args:
            input_file_path: 输入文件路径
            output_file_path: 输出文件路径
            
        Returns:
            转换是否成功
        """
        try:
            print("开始处理数据...")
            
            if not output_file_path:
                base_name = os.path.splitext(os.path.basename(input_file_path))[0]
                output_file_path = f"{base_name}_to_bing.xlsx"
            
            # 读取文件
            df = self._read_file_with_encoding_fallback(input_file_path)
            if df is None:
                return False
            
            # 处理数据
            df = self.process_dataframe(df)
            print('数据清理完成')
            
            # 转换数据格式
            print("正在转换数据格式...")
            new_df = self.create_new_dataframe(df)
            
            # 按模板调整列结构
            new_df = self.align_with_template(new_df)
            
            # 保存Excel
            print("正在保存Excel...")
            new_df.to_excel(output_file_path, index=False)
            print(f"数据处理完成！结果已保存到 {output_file_path}")
            
            return True
            
        except Exception as e:
            print(f"转换过程中发生错误: {e}")
            return False
    
    def convert_uploaded_file(self, uploaded_file, enable_url_parsing: bool = True, 
                            enable_data_cleaning: bool = True, 
                            preserve_original_data: bool = False) -> Optional[pd.DataFrame]:
        """
        转换上传的文件并返回DataFrame
        
        Args:
            uploaded_file: Streamlit上传的文件对象
            enable_url_parsing: 是否启用URL解析
            enable_data_cleaning: 是否启用数据清洗
            preserve_original_data: 是否保留原始数据
            
        Returns:
            转换后的DataFrame或None
        """
        try:
            print(f"开始转换文件: {uploaded_file.name}")
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_input_path = os.path.join(temp_dir, f"temp_baidu_{uploaded_file.name}")
            
            # 保存上传文件
            with open(temp_input_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            print(f"文件已保存到: {temp_input_path}")
            
            # 读取文件
            df = self._read_file_with_encoding_fallback(temp_input_path)
            if df is None or df.empty:
                print("读取文件失败或文件为空")
                return None
            
            print(f"成功读取文件，共 {len(df)} 行数据")
            print(f"文件列名: {list(df.columns)}")
            
            # 根据选项进行数据清洗
            if enable_data_cleaning:
                print("正在清洗数据...")
                df = self.process_dataframe(df)
                print(f"数据清洗完成，剩余 {len(df)} 行")
            
            # 转换为Bing格式
            print("正在转换为Bing格式...")
            result_df = self.create_new_dataframe(df)
            
            # 如果不启用URL解析，清空URL列
            if not enable_url_parsing and 'Final Url' in result_df.columns:
                result_df['Final Url'] = ""
                print("已禁用URL解析")
            
            # 如果保留原始数据，添加原始列
            if preserve_original_data:
                # 添加一些原始数据列
                original_cols_to_keep = ['关键词名称', '推广计划名称', '推广单元名称', '匹配模式', '出价']
                for col in original_cols_to_keep:
                    if col in df.columns:
                        result_df[f'原始_{col}'] = df[col]
                print("已保留原始数据列")
            
            # 按模板调整列结构
            result_df = self.align_with_template(result_df)
            
            print(f"转换完成，共 {len(result_df)} 行")
            print(f"转换后列名: {list(result_df.columns)}")
            
            # 清理临时文件
            if os.path.exists(temp_input_path):
                os.remove(temp_input_path)
            
            return result_df
            
        except Exception as e:
            print(f"转换过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """命令行使用示例 - 按照原始代码逻辑"""
    print("开始处理数据...")
    input_file = "baidu.csv"
    output_file = "baidu_to_bing.xlsx"
    
    converter = BaiduToBingConverter()
    
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return
    
    success = converter.convert_file(input_file, output_file)
    
    if success:
        print("转换成功完成！")
    else:
        print("转换失败！")


if __name__ == '__main__':
    main()
