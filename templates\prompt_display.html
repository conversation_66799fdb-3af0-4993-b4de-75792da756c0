<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEM广告创意提示词 - SEM关键词智能分组工具 V2.0</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-search text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold text-gray-900">SEM关键词智能分组工具</h1>
                    <span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">V2.0</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-bolt mr-1"></i>
                        FastAPI版本
                    </div>
                    <a href="/" class="text-blue-600 hover:text-blue-800 font-semibold text-sm">
                        <i class="fas fa-home mr-1"></i>
                        返回首页
                    </a>
                    <a href="/custom-keywords" class="text-purple-600 hover:text-purple-800 font-semibold text-sm">
                        <i class="fas fa-magic mr-1"></i>
                        关键词拓词
                    </a>
                    <a href="/keyword-sync" class="text-blue-600 hover:text-blue-800 font-semibold text-sm">
                        <i class="fas fa-search mr-1"></i>
                        关键词查询
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-magic text-purple-600 mr-3"></i>
                    SEM广告创意提示词
                </h2>
                <p class="text-gray-600 max-w-3xl mx-auto">
                    专业的SEM广告创意生成提示词，帮助您创建高点击率、高转化率的广告创意
                </p>
            </div>
        </div>

        <!-- 提示词内容展示 -->
        <div class="bg-white rounded-lg shadow-md p-8">
            <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                <div class="text-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">📋 SEM广告创意生成提示词</h3>
                    <p class="text-sm text-gray-600">点击下方文本框，全选复制到DeepSeek中使用</p>
                </div>
                
                <textarea readonly class="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm text-gray-800 bg-white resize-none focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="this.select()">你是一位拥有10年经验的SEM（搜索引擎营销）优化专家，尤其擅长为实用工具软件撰写高点击率、高转化率的广告创意。

**# 任务** 
你的任务是，根据我提供的产品和推广单元信息，为每个推广单元生成2组高质量的SEM广告创意。

**# 规则与限制**
请严格遵守以下规则，这是生成有效广告创意的关键：

1. **关键词插入 (DKI):** 在标题中，必须使用 {关键词} 通配符开头或紧随其后，确保与用户搜索词高度相关。

2. **字符限制（1个汉字=2个字符）：**
   **标题：** 严格控制在 50 个字符以内。
   **描述1：** 关键词插入 (DKI)，必须使用 {组里面关键词}，保证语句通顺 通配符开头或紧随其后，确保与用户搜索词高度相关，严格控制在 80 个字符以内。
   **描述2：** 严格控制在 80 个字符以内。

3. **标题要求：** 格式为"{关键词} - [核心优势/品牌名]"，例如"{PDF转Word} - 在线免费转换,格式不变"。

4. **描述1要求：** 针对推广单元的功能，详细说明工具如何解决问题。例如，对于"PDF转Word"，强调"转换后可编辑，排版精准还原，无需二次修改"。

5. **描述2要求：** 突出工具的通用优势（如安全、多功能、免费）并包含强有力的行动号召（CTA）。例如，"文件加密传输，2小时后自动删除。立即在线免费转换！"

6. **禁止项：** 严禁使用"最"、"第一"、"唯一"等广告法禁止的极限词。

7. **禁止使用广告禁用词：** 严格按照要求，广告创意不要使用官方，破解，首选，第一，免费，最，唯一，NO.1，TOP1，独一无二，全国第一，全网第一，销量第一，排名第一，仅此一家，绝无仅有，史无前例，前无古人，永久，万能，绝对，从未，国家级，世界级，宇宙级，顶级，尖端，极品，王者，至尊，殿堂级，终极，冠军，免费，免费领取，0元购，点击有奖，点击获取，恭喜中奖，抽奖，仅限今日，最后一天，倒计时，随时涨价，抢购，秒杀，疯抢，售罄，售空等禁用词

8. **{关键词}即为核心关键词**

**# 生成格式：推广计划,推广单元,标题,描述1,描述2**
**以表格方式输出**

根据推广计划，推广组，进行广告创意撰写：
- 推广创意标题：{关键词}和{关键词}+组名称相关功能 共(50字符)
- 描述1：{关键词}或{关键词下载}+{组名称相关功能}开头+ 功能介绍 + 卖点 共(80字符)
- 描述2：(10-15字) 描述2最后加上通过软件管家提供安全下载 共(80字符)

**# 示例（替换想要的核心关键词与推广组）：**
核心关键词：罗技ghub
推广计划，推广组
第三方软件-罗技ghub	错拼
第三方软件-罗技ghub	别名</textarea>
                
                <div class="mt-4 text-center">
                    <button onclick="document.querySelector('textarea').select(); document.execCommand('copy'); alert('提示词已复制到剪贴板！');" 
                            class="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        📋 复制提示词
                    </button>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center mt-8">
            <a href="/" class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </a>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    © 2024 SEM关键词智能分组工具 V2.0 - FastAPI版本
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">
                        <i class="fas fa-rocket mr-1"></i>
                        高性能异步处理
                    </span>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
