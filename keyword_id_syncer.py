"""
关键词ID同步模块
负责关键词与数据库的同步，包括查询和入库操作
"""

import pymysql
import pandas as pd
import os
import logging
import time
from typing import List, Dict, Optional, Tuple
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

class KeywordIdSyncer:
    """关键词ID同步器"""
    
    def __init__(self, db_config: dict = None):
        """
        初始化关键词同步器
        
        Args:
            db_config: 数据库配置信息
        """
        self.db_config = db_config or {
            'host': '************',
            'port': 23006,
            'user': 'db_read',
            'password': 'LO5ku9RvVS8BsT1m',
            'database': 'duba_pkg_mgr',
            'charset': 'utf8mb4'
        }
        
        # 加载sem.csv映射表
        self.softid_mapping = self._load_softid_mapping()
        
    def _load_softid_mapping(self) -> Dict[str, int]:
        """加载推广计划名称到softID的映射"""
        try:
            if not os.path.exists('sem.csv'):
                logger.warning("sem.csv文件不存在")
                return {}
            
            df = pd.read_csv('sem.csv', encoding='utf-8')
            mapping = {}
            
            for _, row in df.iterrows():
                plan_name = str(row['计划名']).strip()
                soft_id = int(row['计划ID'])
                mapping[plan_name] = soft_id
            
            logger.info(f"加载了{len(mapping)}个推广计划映射")
            return mapping
            
        except Exception as e:
            logger.error(f"加载sem.csv失败: {str(e)}")
            return {}
    
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config.get('port', 3306),
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset=self.db_config['charset'],
                cursorclass=pymysql.cursors.DictCursor,
                connect_timeout=30,  # 连接超时
                read_timeout=60,     # 读取超时
                write_timeout=60,    # 写入超时
                autocommit=False     # 手动控制事务
            )
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    def load_keywords_from_file(self, file_path: str) -> List[Dict]:
        """
        从文件加载关键词数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含关键词数据的列表
        """
        start_time = time.time()
        try:
            logger.info(f"开始加载文件: {file_path}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            logger.info(f"文件大小: {file_size / (1024*1024):.2f} MB")
            
            # 根据文件扩展名选择读取方式，添加多种编码尝试
            df = None
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            
            if file_path.endswith('.csv'):
                for encoding in encodings:
                    try:
                        logger.info(f"尝试使用编码 {encoding} 读取CSV文件")
                        df = pd.read_csv(file_path, encoding=encoding)
                        logger.info(f"成功使用编码 {encoding} 读取文件")
                        break
                    except UnicodeDecodeError:
                        continue
                if df is None:
                    raise ValueError("无法使用支持的编码读取CSV文件")
                    
            elif file_path.endswith(('.xlsx', '.xls')):
                logger.info("读取Excel文件")
                df = pd.read_excel(file_path)
                
            elif file_path.endswith('.txt'):
                for encoding in encodings:
                    try:
                        logger.info(f"尝试使用编码 {encoding} 读取TXT文件")
                        df = pd.read_csv(file_path, sep='\t', encoding=encoding)
                        logger.info(f"成功使用编码 {encoding} 读取文件")
                        break
                    except UnicodeDecodeError:
                        continue
                if df is None:
                    raise ValueError("无法使用支持的编码读取TXT文件")
            else:
                raise ValueError(f"不支持的文件格式: {file_path}")
            
            logger.info(f"文件读取完成，共 {len(df)} 行数据")
            
            # 检查必要的列
            required_columns = ['推广计划', '推广单元', '关键词']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"文件列信息: {list(df.columns)}")
                raise ValueError(f"文件缺少必要的列: {missing_columns}")
            
            # 移除空行和无效数据
            df = df.dropna(subset=required_columns)
            logger.info(f"清理后剩余 {len(df)} 行有效数据")
            
            # 转换为字典列表
            keywords_data = []
            batch_size = 1000
            
            for i in range(0, len(df), batch_size):
                batch_df = df.iloc[i:i + batch_size]
                for _, row in batch_df.iterrows():
                    try:
                        keywords_data.append({
                            'campaign': str(row['推广计划']).strip(),
                            'ad_group': str(row['推广单元']).strip(),
                            'keyword': str(row['关键词']).strip()
                        })
                    except Exception as row_error:
                        logger.warning(f"跳过无效行 {i + len(keywords_data)}: {str(row_error)}")
                        continue
                
                if i % (batch_size * 10) == 0:  # 每10000行记录一次进度
                    logger.info(f"已处理 {i + len(batch_df)} / {len(df)} 行")
            
            elapsed_time = time.time() - start_time
            logger.info(f"文件加载完成，共{len(keywords_data)}个关键词，耗时{elapsed_time:.2f}秒")
            return keywords_data
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"加载关键词文件失败: {str(e)}，耗时{elapsed_time:.2f}秒")
            raise
    
    def query_keyword_ids(self, keywords: List[str]) -> Dict[str, Dict]:
        """
        查询关键词对应的softID和keyID
        
        Args:
            keywords: 关键词列表
            
        Returns:
            关键词ID映射字典
        """
        try:
            if not keywords:
                return {}
            
            # 去重处理
            unique_keywords = list(set(keywords))
            logger.info(f"去重后查询{len(unique_keywords)}个关键词")
            
            connection = self.get_db_connection()
            keyword_ids = {}
            batch_size = 1000  # 分批查询，避免SQL语句过长
            
            try:
                with connection.cursor() as cursor:
                    # 分批查询
                    for i in range(0, len(unique_keywords), batch_size):
                        batch = unique_keywords[i:i + batch_size]
                        placeholders = ', '.join(['%s'] * len(batch))
                        
                        query = f"""
                            SELECT id, word, softid 
                            FROM keywords 
                            WHERE LOWER(word) IN ({placeholders})
                        """
                        
                        try:
                            # 将查询参数转换为小写
                            batch_lower = [keyword.lower() for keyword in batch]
                            cursor.execute(query, batch_lower)
                            results = cursor.fetchall()
                            
                            # 创建小写到原始关键词的映射
                            lower_to_original = {keyword.lower(): keyword for keyword in batch}
                            
                            for row in results:
                                # 使用原始关键词作为键
                                original_keyword = lower_to_original.get(row['word'].lower())
                                if original_keyword:
                                    keyword_ids[original_keyword] = {
                                        'softid': row['softid'],
                                        'keyid': row['id']
                                    }
                            
                            logger.info(f"批次 {i//batch_size + 1}: 查询到{len(results)}个关键词")
                            
                        except Exception as batch_error:
                            logger.error(f"批次 {i//batch_size + 1} 查询失败: {str(batch_error)}")
                            continue
                            
            finally:
                connection.close()
            
            logger.info(f"总共查询到{len(keyword_ids)}个关键词的ID信息")
            return keyword_ids
            
        except Exception as e:
            logger.error(f"查询关键词ID失败: {str(e)}")
            raise
    
    def filter_existing_keywords(self, keywords_to_check: List[Dict]) -> List[Dict]:
        """
        过滤掉已存在的关键词，只返回需要插入的关键词
        
        Args:
            keywords_to_check: 需要检查的关键词列表
            
        Returns:
            需要插入的关键词列表
        """
        try:
            if not keywords_to_check:
                return []
            
            # 提取关键词进行查询
            words_to_check = [item['word'] for item in keywords_to_check]
            existing_keywords = self.query_keyword_ids(words_to_check)
            
            # 过滤掉已存在的关键词
            filtered_keywords = []
            for item in keywords_to_check:
                if item['word'] not in existing_keywords:
                    filtered_keywords.append(item)
            
            logger.info(f"过滤后需要插入{len(filtered_keywords)}个关键词（原始{len(keywords_to_check)}个）")
            return filtered_keywords
            
        except Exception as e:
            logger.error(f"过滤关键词失败: {str(e)}")
            return keywords_to_check  # 出错时返回原始列表

    def insert_keywords(self, keywords_to_insert: List[Dict]) -> int:
        """
        批量插入关键词到数据库
        
        Args:
            keywords_to_insert: 要插入的关键词列表
            
        Returns:
            插入的记录数
        """
        start_time = time.time()
        try:
            if not keywords_to_insert:
                return 0
            
            # 去重处理，避免重复插入
            unique_keywords = {}
            for item in keywords_to_insert:
                key = (item['word'], item['softid'])
                if key not in unique_keywords:
                    unique_keywords[key] = item
            
            keywords_to_insert = list(unique_keywords.values())
            logger.info(f"去重后准备插入{len(keywords_to_insert)}个关键词")
            
            connection = self.get_db_connection()
            total_inserted = 0
            batch_size = 1000  # 分批处理，提高性能
            
            try:
                with connection.cursor() as cursor:
                    # 使用 INSERT IGNORE 避免重复键错误
                    insert_query = """
                        INSERT IGNORE INTO keywords (word, softid, enable, operator, type, keywordid, create_time, update_time)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    current_time = datetime.now()
                    
                    # 分批处理
                    for i in range(0, len(keywords_to_insert), batch_size):
                        batch = keywords_to_insert[i:i + batch_size]
                        insert_data = []
                        
                        for item in batch:
                            insert_data.append((
                                item['word'],
                                item['softid'],
                                1,  # enable: 默认启用
                                'system',  # operator: 系统自动添加
                                1,  # type: 自动化生成
                                0,  # keywordid: 百度关键词id，默认为0
                                current_time,  # create_time
                                current_time   # update_time
                            ))
                        
                        try:
                            cursor.executemany(insert_query, insert_data)
                            batch_inserted = cursor.rowcount
                            total_inserted += batch_inserted
                            logger.info(f"批次 {i//batch_size + 1}: 插入了{batch_inserted}个关键词")
                            
                        except Exception as batch_error:
                            logger.error(f"批次 {i//batch_size + 1} 插入失败: {str(batch_error)}")
                            # 继续处理下一批次
                            continue
                    
                    connection.commit()
                    
            except Exception as e:
                connection.rollback()
                logger.error(f"插入过程中发生错误: {str(e)}")
                raise
            finally:
                connection.close()
            
            elapsed_time = time.time() - start_time
            logger.info(f"总共成功插入{total_inserted}个关键词，耗时{elapsed_time:.2f}秒")
            return total_inserted
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"插入关键词失败: {str(e)}，耗时{elapsed_time:.2f}秒")
            raise
    
    def process_keywords(self, keywords_data: List[Dict], enable_import: bool = False, status_callback=None) -> Dict:
        """
        处理关键词数据，查询并可选择导入
        
        Args:
            keywords_data: 关键词数据列表
            enable_import: 是否启用导入功能
            status_callback: 状态更新回调函数
            
        Returns:
            处理结果
        """
        try:
            # 提取所有关键词
            all_keywords = [item['keyword'] for item in keywords_data]
            
            if status_callback:
                status_callback.update("查询数据库", 45, f"正在查询 {len(all_keywords)} 个关键词...")
            
            # 查询现有的关键词ID
            existing_keyword_ids = self.query_keyword_ids(all_keywords)
            
            if status_callback:
                status_callback.update("分析结果", 60, f"找到 {len(existing_keyword_ids)} 个已存在的关键词")
            
            # 分离已存在和未找到的关键词
            found_keywords = []
            missing_keywords = []
            can_import_keywords = []
            cannot_import_keywords = []
            
            for item in keywords_data:
                keyword = item['keyword']
                campaign = item['campaign']
                
                if keyword in existing_keyword_ids:
                    # 已存在的关键词
                    found_keywords.append({
                        'keyword': keyword,
                        'campaign': campaign,
                        'ad_group': item['ad_group'],
                        'softid': existing_keyword_ids[keyword]['softid'],
                        'keyid': existing_keyword_ids[keyword]['keyid'],
                        'url': f"https://sem.duba.net/sem/dseek/f{existing_keyword_ids[keyword]['softid']}.html?sfrom=196&TFT=16&keyID={existing_keyword_ids[keyword]['keyid']}",
                        'status': '已存在'
                    })
                else:
                    # 未找到的关键词
                    softid = self.softid_mapping.get(campaign)
                    if softid:
                        # 有对应的softID，可以导入
                        can_import_keywords.append(item)
                        missing_keywords.append({
                            'keyword': keyword,
                            'campaign': campaign,
                            'ad_group': item['ad_group'],
                            'softid': softid,
                            'keyid': 'N/A',
                            'url': 'N/A',
                            'status': '未找到'
                        })
                    else:
                        # 没有对应的softID，无法导入
                        cannot_import_keywords.append(item)
                        missing_keywords.append({
                            'keyword': keyword,
                            'campaign': campaign,
                            'ad_group': item['ad_group'],
                            'softid': 'N/A',
                            'keyid': 'N/A',
                            'url': 'N/A',
                            'status': '推广计划不存在'
                        })
            
            # 处理导入功能
            imported_keywords = []
            if enable_import and can_import_keywords:
                try:
                    if status_callback:
                        status_callback.update("准备导入", 70, f"准备导入 {len(can_import_keywords)} 个关键词...")
                    
                    # 按推广计划分组
                    campaign_groups = {}
                    for item in can_import_keywords:
                        campaign = item['campaign']
                        if campaign not in campaign_groups:
                            campaign_groups[campaign] = []
                        campaign_groups[campaign].append(item)
                    
                    # 准备导入数据
                    keywords_to_insert = []
                    for campaign, group_keywords in campaign_groups.items():
                        softid = self.softid_mapping.get(campaign)
                        if softid:
                            for item in group_keywords:
                                keywords_to_insert.append({
                                    'word': item['keyword'],
                                    'softid': softid
                                })
                    
                    # 执行导入
                    if keywords_to_insert:
                        if status_callback:
                            status_callback.update("过滤重复关键词", 72, f"正在过滤 {len(keywords_to_insert)} 个关键词...")
                        
                        # 预先过滤已存在的关键词，提高插入效率
                        filtered_keywords = self.filter_existing_keywords(keywords_to_insert)
                        
                        if filtered_keywords:
                            if status_callback:
                                status_callback.update("导入关键词", 75, f"正在导入 {len(filtered_keywords)} 个新关键词到数据库...")
                            
                            inserted_count = self.insert_keywords(filtered_keywords)
                        else:
                            inserted_count = 0
                            logger.info("所有关键词都已存在，无需插入")
                        
                        # 重新查询已导入的关键词获取keyID
                        if inserted_count > 0:
                            imported_words = [item['word'] for item in keywords_to_insert]
                            new_keyword_ids = self.query_keyword_ids(imported_words)
                            
                            # 更新missing_keywords中对应的记录
                            for i, missing_item in enumerate(missing_keywords):
                                if missing_item['keyword'] in new_keyword_ids and missing_item['status'] == '未找到':
                                    keyid = new_keyword_ids[missing_item['keyword']]['keyid']
                                    missing_keywords[i]['keyid'] = keyid
                                    missing_keywords[i]['url'] = f"https://sem.duba.net/sem/dseek/f{missing_item['softid']}.html?sfrom=196&TFT=16&keyID={keyid}"
                                    missing_keywords[i]['status'] = '已导入'
                                    
                                    imported_keywords.append(missing_keywords[i])
                        
                        logger.info(f"成功导入{inserted_count}个关键词")
                        
                except Exception as e:
                    logger.error(f"导入关键词失败: {str(e)}")
            
            # 构建返回结果
            result = {
                'total_keywords': len(keywords_data),
                'found_keywords': found_keywords,
                'missing_keywords': missing_keywords,
                'imported_keywords': imported_keywords,
                'found_count': len(found_keywords),
                'missing_count': len([item for item in missing_keywords if item['status'] in ['未找到', '推广计划不存在']]),
                'imported_count': len(imported_keywords),
                'cannot_import_count': len(cannot_import_keywords),
                'missing_campaigns': list(set([item['campaign'] for item in cannot_import_keywords])),
                'enable_import': enable_import
            }
            
            action_type = "查询和导入" if enable_import else "查询"
            logger.info(f"{action_type}完成：总数{result['total_keywords']}，已存在{result['found_count']}，未找到{result['missing_count']}，已导入{result['imported_count']}")
            
            return result
            
        except Exception as e:
            logger.error(f"查询关键词失败: {str(e)}")
            raise
    
    def generate_url(self, softid: int, keyid: int) -> str:
        """生成关键词URL"""
        return f"https://sem.duba.net/sem/dseek/f{softid}.html?sfrom=196&TFT=16&keyID={keyid}"
    
    def export_results(self, results: Dict) -> pd.DataFrame:
        """
        导出查询结果为DataFrame
        
        Args:
            results: 查询结果
            
        Returns:
            包含所有关键词信息的DataFrame
        """
        try:
            all_keywords = []
            
            # 合并已存在和未找到的关键词
            for keyword_data in results['found_keywords'] + results['missing_keywords']:
                all_keywords.append({
                    '关键词': keyword_data['keyword'],
                    '推广计划': keyword_data['campaign'],
                    '推广单元': keyword_data['ad_group'],
                    'SoftID': keyword_data['softid'],
                    'KeyID': keyword_data['keyid'],
                    'URL': keyword_data['url'],
                    '状态': keyword_data['status']
                })
            
            df = pd.DataFrame(all_keywords)
            return df
            
        except Exception as e:
            logger.error(f"导出结果失败: {str(e)}")
            raise