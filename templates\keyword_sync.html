<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词ID查询工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 3rem;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        .progress-container {
            display: none;
            margin-top: 2rem;
        }
        
        .result-container {
            display: none;
            margin-top: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin: 0;
        }
        
        .stat-card p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .db-test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            display: none;
        }
        
        .db-test-result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .db-test-result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .keyword-preview {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        
        .warning-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .warning-section h5 {
            color: #856404;
            margin-bottom: 0.5rem;
        }
        
        .warning-section p {
            color: #856404;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-search text-primary"></i>
                    关键词ID查询工具
                </h1>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回首页
                        </a>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-info" onclick="showHelp()">
                            <i class="fas fa-question-circle"></i> 使用帮助
                        </button>
                    </div>
                </div>
                
                <!-- 数据库连接状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-database"></i> 数据库连接状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-primary" onclick="testDatabaseConnection()">
                                    <i class="fas fa-plug"></i> 测试数据库连接
                                </button>
                                <div id="db-test-result" class="db-test-result"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件上传区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload"></i> 上传关键词文件
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="warning-section">
                            <h5><i class="fas fa-exclamation-triangle"></i> 文件格式要求</h5>
                            <p>请确保您的文件包含以下三列：<strong>推广计划</strong>、<strong>推广单元</strong>、<strong>关键词</strong></p>
                            <p>支持的文件格式：CSV、Excel (.xlsx/.xls)、TXT (制表符分隔)</p>
                        </div>
                        
                        <div class="upload-area" id="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>拖拽文件到此处或点击选择</h5>
                            <p class="text-muted">支持 CSV、Excel、TXT 格式，最大 200MB</p>
                            <input type="file" id="file-input" accept=".csv,.xlsx,.xls,.txt" style="display: none;">
                        </div>
                        
                        <div id="file-info" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-file"></i>
                                <strong>已选择文件：</strong> <span id="file-name"></span>
                                <br>
                                <small><strong>大小：</strong> <span id="file-size"></span></small>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" value="" id="enable_import">
                                <label class="form-check-label" for="enable_import">
                                    <strong>启用导入功能</strong> - 将未找到的关键词导入到数据库（仅限有对应推广计划的关键词）
                                </label>
                            </div>
                            
                            <div class="text-center">
                                <button class="btn btn-success btn-lg" id="sync-btn" onclick="startKeywordQuery()" disabled>
                                    <i class="fas fa-search"></i> 开始查询关键词
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 进度显示 -->
                <div class="progress-container" id="progress-container">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-spinner fa-spin"></i> 处理中...
                            </h5>
                            <div class="progress mb-3" style="height: 20px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progress-bar" 
                                     role="progressbar" 
                                     style="width: 0%">
                                    0%
                                </div>
                            </div>
                            <p class="mb-0">
                                <strong>当前步骤：</strong> <span id="current-step">准备中...</span>
                            </p>
                            <p class="text-muted">
                                <small id="progress-message">正在初始化...</small>
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 结果显示 -->
                <div class="result-container" id="result-container">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar"></i> 查询结果
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row" id="stats-row">
                                <!-- 统计信息将在这里动态生成 -->
                            </div>
                            
                            <div class="alert alert-info mt-3">
                                <h6><i class="fas fa-info-circle"></i> 查询详情</h6>
                                <p id="sync-details">查询完成，详细信息将在这里显示。</p>
                            </div>
                            
                            <div class="mt-3 text-center">
                                <button class="btn btn-primary btn-lg" id="export-btn" onclick="exportResults()">
                                    <i class="fas fa-download"></i> 导出结果
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 错误显示 -->
                <div class="alert alert-danger" id="error-container" style="display: none;">
                    <h5><i class="fas fa-exclamation-triangle"></i> 错误</h5>
                    <p id="error-message"></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="helpModalLabel">
                        <i class="fas fa-question-circle"></i> 使用帮助
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>功能说明</h6>
                    <p>本工具用于查询关键词是否存在于MySQL数据库中，并显示对应的URL链接。支持可选的导入功能，将未找到的关键词导入到数据库。数据库连接信息已内置，无需用户配置。</p>
                    
                    <h6>使用步骤</h6>
                    <ol>
                        <li>测试数据库连接</li>
                        <li>上传包含关键词的文件</li>
                        <li>选择是否启用导入功能</li>
                        <li>点击"开始查询关键词"</li>
                        <li>等待查询完成</li>
                        <li>导出结果</li>
                    </ol>
                    
                    <h6>文件格式要求</h6>
                    <ul>
                        <li>必须包含三列：推广计划、推广单元、关键词</li>
                        <li>支持CSV、Excel (.xlsx/.xls)、TXT格式</li>
                        <li>文件大小不超过200MB</li>
                        <li>推广计划名称需要在sem.csv中有对应的softID映射</li>
                    </ul>
                    
                    <h6>查询逻辑</h6>
                    <ul>
                        <li>系统会查询数据库中是否已存在相同的关键词</li>
                        <li>如果存在，显示现有的softID和keyID</li>
                        <li>如果不存在，显示"未找到"状态</li>
                        <li>根据推广计划名称在sem.csv中查找对应的softID</li>
                        <li>为已存在的关键词生成URL格式：https://sem.duba.net/sem/dseek/f{softid}.html?sfrom=196&TFT=16&keyID={keyid}</li>
                    </ul>
                    
                    <h6>导入功能</h6>
                    <ul>
                        <li>启用导入功能后，系统会自动将未找到的关键词导入到数据库</li>
                        <li>只有在sem.csv中找到对应推广计划的关键词才会被导入</li>
                        <li>无法找到对应推广计划的关键词不会被导入，会显示"推广计划不存在"状态</li>
                        <li>导入成功的关键词会自动分配新的keyID并生成对应的URL</li>
                        <li>导入的关键词状态会显示为"已导入"</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentTaskId = null;
        let selectedFile = null;
        let processingInterval = null;
        
        // 文件上传处理
        document.getElementById('upload-area').addEventListener('click', function() {
            document.getElementById('file-input').click();
        });
        
        document.getElementById('file-input').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileSelect(file);
            }
        });
        
        // 拖拽上传
        const uploadArea = document.getElementById('upload-area');
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const file = e.dataTransfer.files[0];
            if (file) {
                handleFileSelect(file);
            }
        });
        
        function handleFileSelect(file) {
            selectedFile = file;
            
            // 显示文件信息
            document.getElementById('file-name').textContent = file.name;
            document.getElementById('file-size').textContent = formatFileSize(file.size);
            document.getElementById('file-info').style.display = 'block';
            
            // 启用开始按钮
            document.getElementById('sync-btn').disabled = false;
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 测试数据库连接
        function testDatabaseConnection() {
            fetch('/api/test-db-connection')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const connectionInfo = data.connection_info;
                        showDbTestResult(`连接成功！数据库：${connectionInfo.host}:${connectionInfo.port}/${connectionInfo.database}，共有 ${data.keywords_count} 个关键词记录`, 'success');
                    } else {
                        showDbTestResult(`连接失败：${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showDbTestResult(`连接失败：${error.message}`, 'error');
                });
        }
        
        function showDbTestResult(message, type) {
            const resultDiv = document.getElementById('db-test-result');
            resultDiv.textContent = message;
            resultDiv.className = `db-test-result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        // 开始查询关键词
        function startKeywordQuery() {
            if (!selectedFile) {
                alert('请先选择要上传的文件');
                return;
            }
            
            const enableImport = document.getElementById('enable_import').checked;
            
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('enable_import', enableImport);
            
            // 显示进度
            document.getElementById('progress-container').style.display = 'block';
            document.getElementById('result-container').style.display = 'none';
            document.getElementById('error-container').style.display = 'none';
            document.getElementById('sync-btn').disabled = true;
            
            fetch('/api/upload-keyword-sync-file', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    checkProcessingStatus();
                } else {
                    showError(data.error || '上传失败');
                }
            })
            .catch(error => {
                showError('上传失败：' + error.message);
            });
        }
        
        // 检查处理状态
        function checkProcessingStatus() {
            if (!currentTaskId) return;
            
            processingInterval = setInterval(() => {
                fetch(`/api/status/${currentTaskId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateProgress(data.progress, data.current_step, data.message);
                            
                            if (data.status === 'completed') {
                                clearInterval(processingInterval);
                                showResults(data.result);
                            } else if (data.status === 'failed') {
                                clearInterval(processingInterval);
                                showError(data.error || '处理失败');
                            }
                        }
                    })
                    .catch(error => {
                        clearInterval(processingInterval);
                        showError('状态查询失败：' + error.message);
                    });
            }, 1000);
        }
        
        function updateProgress(progress, step, message) {
            const progressBar = document.getElementById('progress-bar');
            progressBar.style.width = progress + '%';
            progressBar.textContent = progress + '%';
            
            document.getElementById('current-step').textContent = step;
            document.getElementById('progress-message').textContent = message;
        }
        
        function showResults(result) {
            document.getElementById('progress-container').style.display = 'none';
            document.getElementById('result-container').style.display = 'block';
            document.getElementById('sync-btn').disabled = false;
            
            const stats = result.statistics;
            const statsRow = document.getElementById('stats-row');
            
            let statsHtml = `
                <div class="col-md-3">
                    <div class="stat-card">
                        <h3>${stats.total_keywords}</h3>
                        <p>总关键词数</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h3>${stats.found_count}</h3>
                        <p>已存在关键词</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h3>${stats.missing_count}</h3>
                        <p>未找到关键词</p>
                    </div>
                </div>
            `;
            
            if (stats.enable_import) {
                statsHtml += `
                    <div class="col-md-3">
                        <div class="stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                            <h3>${stats.imported_count}</h3>
                            <p>已导入关键词</p>
                        </div>
                    </div>
                `;
                
                if (stats.cannot_import_count > 0) {
                    statsHtml += `
                        <div class="col-md-3">
                            <div class="stat-card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                                <h3>${stats.cannot_import_count}</h3>
                                <p>无法导入关键词</p>
                            </div>
                        </div>
                    `;
                }
            } else {
                statsHtml += `
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3>${stats.missing_campaigns_count}</h3>
                            <p>未找到推广计划</p>
                        </div>
                    </div>
                `;
            }
            
            statsRow.innerHTML = statsHtml;
            
            let detailsText = `成功查询 ${stats.total_keywords} 个关键词。`;
            if (stats.found_count > 0) {
                detailsText += `其中 ${stats.found_count} 个已存在于数据库中。`;
            }
            if (stats.enable_import) {
                if (stats.imported_count > 0) {
                    detailsText += `成功导入 ${stats.imported_count} 个关键词到数据库。`;
                }
                if (stats.cannot_import_count > 0) {
                    detailsText += `有 ${stats.cannot_import_count} 个关键词因为无法找到对应的推广计划而无法导入。`;
                }
                if (stats.missing_count > 0) {
                    detailsText += `${stats.missing_count} 个关键词在数据库中未找到。`;
                }
            } else {
                if (stats.missing_count > 0) {
                    detailsText += `${stats.missing_count} 个关键词在数据库中未找到。`;
                }
                if (stats.missing_campaigns_count > 0) {
                    detailsText += `注意：${stats.missing_campaigns_count} 个关键词因为无法找到对应的推广计划。`;
                }
            }
            
            document.getElementById('sync-details').textContent = detailsText;
        }
        
        function showError(message) {
            document.getElementById('progress-container').style.display = 'none';
            document.getElementById('result-container').style.display = 'none';
            document.getElementById('error-container').style.display = 'block';
            document.getElementById('error-message').textContent = message;
            document.getElementById('sync-btn').disabled = false;
        }
        
        // 导出结果
        function exportResults() {
            if (!currentTaskId) {
                alert('没有可导出的结果');
                return;
            }
            
            fetch(`/api/export-keyword-sync-result/${currentTaskId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    alert(`导出成功！文件包含 ${data.rows_count} 条记录`);
                } else {
                    alert('导出失败：' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                alert('导出失败：' + error.message);
            });
        }
        
        // 显示帮助
        function showHelp() {
            const helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
            helpModal.show();
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动测试数据库连接
            testDatabaseConnection();
        });
    </script>
</body>
</html> 