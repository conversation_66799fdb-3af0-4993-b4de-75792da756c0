#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEM关键词智能分组工具 - MVP版本
实现基于词根的关键词分组功能
"""

import pandas as pd
import jieba
import argparse
import os
import sys
import tempfile
from collections import defaultdict, Counter
from typing import List, Dict, Set, Tuple, Optional
import re
from baidu_to_bing_converter import BaiduToBingConverter


class KeywordGrouper:
    """关键词分组器"""
    
    def __init__(self, min_group_size: int = 3):
        """
        初始化分组器
        
        Args:
            min_group_size: 最小分组大小，少于此数量的关键词不单独成组
        """
        self.min_group_size = min_group_size
        self.negative_words: Set[str] = set()
        self.original_keywords: List[str] = []
        self.cleaned_keywords: List[str] = []
        self.filtered_keywords: List[str] = []
        self.groups: Dict[str, List[str]] = {}
        self.use_advanced_brand_algorithm: bool = True  # 默认使用改进算法
        self.use_ngram_analysis: bool = True  # 默认启用n-gram分析
        self.baidu_to_bing_converter = BaiduToBingConverter()  # 百度转Bing转换器
        
    def load_keywords_from_file(self, file_path: str, keyword_column: str = None) -> List[str]:
        """
        从文件加载关键词，支持多种编码格式的自动识别
        
        Args:
            file_path: 文件路径
            keyword_column: CSV文件中关键词列名（可选）
            
        Returns:
            关键词列表
        """
        try:
            keywords = []
            
            if file_path.endswith('.txt'):
                # TXT文件处理 - 支持多种编码
                keywords = self._read_txt_with_encoding_fallback(file_path)
                
            elif file_path.endswith('.csv'):
                # CSV文件处理 - 支持多种编码
                df = self._read_csv_with_encoding_fallback(file_path)
                if df is not None:
                    df = self._clean_dataframe(df)
                    
                    if keyword_column:
                        if keyword_column not in df.columns:
                            raise ValueError(f"列 '{keyword_column}' 不存在于文件中")
                        keywords = df[keyword_column].dropna().astype(str).tolist()
                    else:
                        # 自动选择第一列作为关键词列
                        keywords = df.iloc[:, 0].dropna().astype(str).tolist()
                        
            elif file_path.endswith('.xlsx'):
                # Excel文件处理
                try:
                    df = pd.read_excel(file_path)
                    df = self._clean_dataframe(df)
                    
                    if keyword_column:
                        if keyword_column not in df.columns:
                            raise ValueError(f"列 '{keyword_column}' 不存在于文件中")
                        keywords = df[keyword_column].dropna().astype(str).tolist()
                    else:
                        # 自动选择第一列作为关键词列
                        keywords = df.iloc[:, 0].dropna().astype(str).tolist()
                except Exception as e:
                    raise ValueError(f"读取Excel文件失败: {e}")
                    
            else:
                raise ValueError("不支持的文件格式，请使用 .txt, .csv 或 .xlsx 文件")
            
            # 清理和去重关键词
            keywords = self._clean_keywords_list(keywords)
            
            if not keywords:
                print("警告: 未能从文件中提取到有效的关键词")
                return []
                
            self.original_keywords = keywords
            print(f"成功加载 {len(keywords)} 个关键词")
            return keywords
            
        except Exception as e:
            print(f"加载关键词文件失败: {e}")
            return []
    
    def _read_txt_with_encoding_fallback(self, file_path: str) -> List[str]:
        """
        读取TXT文件，支持多种编码格式的自动回退
        
        Args:
            file_path: 文件路径
            
        Returns:
            关键词列表
        """
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin-1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    keywords = [line.strip() for line in f if line.strip()]
                    if keywords:  # 如果成功读取到内容
                        print(f"TXT文件编码识别成功: {encoding}")
                        return keywords
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                print(f"使用编码 {encoding} 读取TXT文件时出错: {e}")
                continue
        
        raise ValueError("无法识别TXT文件编码，请检查文件格式")
    
    def _read_csv_with_encoding_fallback(self, file_path: str) -> pd.DataFrame:
        """
        读取CSV文件，支持多种编码格式的自动回退
        
        Args:
            file_path: 文件路径
            
        Returns:
            DataFrame对象
        """
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin-1']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                if not df.empty:  # 如果成功读取到内容
                    print(f"CSV文件编码识别成功: {encoding}")
                    return df
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                print(f"使用编码 {encoding} 读取CSV文件时出错: {e}")
                continue
        
        raise ValueError("无法识别CSV文件编码，请检查文件格式")
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理DataFrame数据
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清理后的DataFrame
        """
        if df is None or df.empty:
            return df
            
        # 去除完全为空的行
        df = df.dropna(how='all')
        
        # 去除重复行
        df = df.drop_duplicates()
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        return df
    
    def _clean_keywords_list(self, keywords: List[str]) -> List[str]:
        """
        清理关键词列表
        
        Args:
            keywords: 原始关键词列表
            
        Returns:
            清理后的关键词列表
        """
        if not keywords:
            return []
            
        cleaned_keywords = []
        seen = set()
        
        for keyword in keywords:
            if keyword is None:
                continue
                
            # 转换为字符串并清理
            clean_keyword = str(keyword).strip()
            
            # 过滤空字符串和无效字符串
            if not clean_keyword or clean_keyword.lower() in ['nan', 'null', 'none', '']:
                continue
                
            # 去重（不区分大小写）
            keyword_lower = clean_keyword.lower()
            if keyword_lower not in seen:
                cleaned_keywords.append(clean_keyword)
                seen.add(keyword_lower)
        
        return cleaned_keywords
    
    def load_negative_words(self, file_path: str) -> Set[str]:
        """
        加载否定词列表
        
        Args:
            file_path: 否定词文件路径
            
        Returns:
            否定词集合
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                negative_words = {line.strip().lower() for line in f if line.strip()}
            
            self.negative_words = negative_words
            print(f"成功加载 {len(negative_words)} 个否定词")
            return negative_words
            
        except Exception as e:
            print(f"加载否定词文件失败: {e}")
            return set()
    
    def clean_keywords(self, keywords: List[str]) -> List[str]:
        """
        清洗关键词：去重、标准化
        
        Args:
            keywords: 原始关键词列表
            
        Returns:
            清洗后的关键词列表
        """
        # 标准化处理：转小写、去除前后空格
        cleaned = []
        seen = set()
        
        for keyword in keywords:
            # 标准化
            clean_keyword = keyword.strip().lower()
            
            # 去重
            if clean_keyword and clean_keyword not in seen:
                cleaned.append(clean_keyword)
                seen.add(clean_keyword)
        
        self.cleaned_keywords = cleaned
        print(f"清洗完成，去重后剩余 {len(cleaned)} 个关键词")
        return cleaned
    
    def filter_keywords(self, keywords: List[str]) -> Tuple[List[str], List[str]]:
        """
        根据否定词过滤关键词
        
        Args:
            keywords: 待过滤的关键词列表
            
        Returns:
            (过滤后的关键词列表, 被过滤的关键词列表)
        """
        if not self.negative_words:
            self.filtered_keywords = keywords
            return keywords, []
        
        filtered = []
        removed = []
        
        for keyword in keywords:
            # 检查是否包含否定词
            contains_negative = any(neg_word in keyword.lower() for neg_word in self.negative_words)
            
            if contains_negative:
                removed.append(keyword)
            else:
                filtered.append(keyword)
        
        self.filtered_keywords = filtered
        print(f"否定词过滤完成，过滤掉 {len(removed)} 个关键词，剩余 {len(filtered)} 个")
        
        # 显示被过滤的关键词
        if removed:
            print("被否定词过滤的关键词:")
            for keyword in removed[:10]:  # 只显示前10个
                print(f"  - {keyword}")
            if len(removed) > 10:
                print(f"  ... 还有 {len(removed) - 10} 个关键词被过滤")
        
        return filtered, removed
    
    def extract_core_words(self, keyword: str, min_word_length: int = 2) -> List[str]:
        """
        提取关键词的核心词
        
        Args:
            keyword: 关键词
            min_word_length: 最小词长度
            
        Returns:
            核心词列表
        """
        # 使用jieba分词
        words = jieba.lcut(keyword)
        
        # 过滤掉长度过短的词和常见停用词
        stop_words = {'的', '是', '在', '了', '和', '与', '或', '及', '等', '有', '无', '不', '很', '最', '更'}
        
        core_words = []
        for word in words:
            if len(word) >= min_word_length and word not in stop_words:
                core_words.append(word)
        
        return core_words
    
    def group_by_download_software_rules(self, keywords: List[str]) -> Dict[str, List[str]]:
        """
        基于规则的关键词分组 - 专门针对电脑下载类软件
        
        Args:
            keywords: 关键词列表
            
        Returns:
            分组结果字典
        """
        # 获取核心品牌词，用于去重
        core_brand = self._extract_core_brand()
        
        # 定义分组规则（按优先级排序）
        RULES = [
            ("域名词", [".org", ".net", ".com", "www.", "http://", "官网", "官方"]),
            ("电脑词", ["pc", "电脑","电脑版", "pc版",]),
            ("下载词", ["下载", "download", "安装包", "安装版", "setup", "安装", "获取"]),
            ("疑问词", ["怎么", "如何", "是什么", "哪个好", "好用吗", "安全吗", "免费吗", "为什么", "什么", "干嘛用的"]),
            ("对比词", ["和", "与", "对比", "比较", "哪个", "还是", "vs", "比", "更好"]),
            ("平台词", ["windows", "win10", "winxp", "64位", "32位", "x64", "x86", "bit", "32", "64"]),
            ("版本词", ["最新版", "新版", "旧版", "版本", "绿色版", "纯净版", "精简版", "整合版", "增强版", "优化版", "免安装", "便携版", "portable"]),
            ("功能词", ["解码", "播放", "字幕", "皮肤", "设置", "配置", "硬解", "编解码", "功能", "特点", "使用", "教程", "提取", "修复"]),
        ]
        
        groups = {}
        used_keywords = set()
        
        # 按规则优先级分组
        for group_name, rule_keywords in RULES:
            group_matches = []
            
            for keyword in keywords:
                if keyword in used_keywords:
                    continue
                
                # 跳过核心品牌词，避免重复
                if core_brand and keyword.lower() == core_brand.lower():
                    used_keywords.add(keyword)
                    continue
                    
                # 规则匹配检查
                keyword_lower = keyword.lower()
                if any(rule_word.lower() in keyword_lower for rule_word in rule_keywords):
                    group_matches.append(keyword)
            
            # 如果匹配到关键词且数量达到最小分组要求
            if group_matches and len(group_matches) >= self.min_group_size:
                groups[group_name] = group_matches
                used_keywords.update(group_matches)
                print(f"  {group_name}: 匹配到 {len(group_matches)} 个关键词")
        
        # 处理未分组的关键词 - 按原有逻辑进行词根分组
        ungrouped = [kw for kw in keywords if kw not in used_keywords]
        # 再次过滤核心品牌词
        if core_brand:
            ungrouped = [kw for kw in ungrouped if kw.lower() != core_brand.lower()]
        
        if ungrouped:
            print(f"  未匹配规则的关键词: {len(ungrouped)} 个，使用词根分组")
            
            # 对未分组关键词使用原有的词根分组逻辑
            word_groups = self._group_by_word_roots(ungrouped)
            
            # 合并词根分组结果
            for group_name, group_keywords in word_groups.items():
                if len(group_keywords) >= self.min_group_size:
                    # 避免组名冲突
                    final_group_name = group_name
                    counter = 1
                    while final_group_name in groups:
                        final_group_name = f"{group_name}_{counter}"
                        counter += 1
                    
                    groups[final_group_name] = group_keywords
                    used_keywords.update(group_keywords)
                else:
                    # 数量不足的关键词合并到"其他"组
                    if "其他" not in groups:
                        groups["其他"] = []
                    groups["其他"].extend(group_keywords)
                    used_keywords.update(group_keywords)
        
        # 最终检查：确保所有关键词都被分组
        final_ungrouped = [kw for kw in keywords if kw not in used_keywords]
        if final_ungrouped:
            if "其他" not in groups:
                groups["其他"] = []
            groups["其他"].extend(final_ungrouped)
        
        self.groups = groups
        print(f"分组完成，共生成 {len(groups)} 个分组")
        return groups
    
    def _group_by_word_roots(self, keywords: List[str]) -> Dict[str, List[str]]:
        """
        对关键词进行词根分组（原有逻辑的简化版本）
        
        Args:
            keywords: 关键词列表
            
        Returns:
            分组结果字典
        """
        # 为每个关键词提取核心词
        keyword_words = {}
        word_keyword_map = defaultdict(list)
        
        for keyword in keywords:
            core_words = self.extract_core_words(keyword)
            keyword_words[keyword] = core_words
            
            # 建立词到关键词的映射
            for word in core_words:
                word_keyword_map[word].append(keyword)
        
        groups = {}
        used_keywords = set()
        
        # 按词频排序，优先处理高频词
        word_freq = Counter()
        for words in keyword_words.values():
            word_freq.update(words)
        
        # 处理包含高频词的关键词
        for word, freq in word_freq.most_common():
            if freq < 2:  # 降低词根分组的最小要求
                continue
                
            candidate_keywords = [kw for kw in word_keyword_map[word] if kw not in used_keywords]
            
            if len(candidate_keywords) >= 2:
                groups[word] = candidate_keywords
                used_keywords.update(candidate_keywords)
        
        return groups
    
    def group_by_common_words(self, keywords: List[str]) -> Dict[str, List[str]]:
        """
        保持原有方法名，内部调用新的分组逻辑
        """
        return self.group_by_download_software_rules(keywords)
    
    def export_results(self, output_file: str) -> bool:
        """
        导出分组结果到CSV文件
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            # 获取核心品牌词
            core_brand = self._extract_core_brand()
            campaign_name = f"第三方软件-{core_brand}" if core_brand else "第三方软件"
            
            results = []
            
            # 先添加核心品牌词组（如果有核心品牌词）
            if core_brand:
                results.append({
                    'Campaign': campaign_name,
                    'Ad Group': '核心品牌词组',
                    'Keyword': core_brand,
                    'Original Keyword': core_brand
                })
            
            # 添加其他分组
            for group_name, group_keywords in self.groups.items():
                for keyword in group_keywords:
                    # 跳过核心品牌词，避免重复
                    if core_brand and keyword.lower() == core_brand.lower():
                        continue
                        
                    # 找到对应的原始关键词
                    original_keyword = ""
                    for orig in self.original_keywords:
                        if orig.strip().lower() == keyword:
                            original_keyword = orig
                            break
                    
                    results.append({
                        'Campaign': campaign_name,
                        'Ad Group': group_name,
                        'Keyword': keyword,
                        'Original Keyword': original_keyword or keyword
                    })
            
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"结果已导出到: {output_file}")
            return True
            
        except Exception as e:
            print(f"导出结果失败: {e}")
            return False
    
    def generate_report(self) -> str:
        """
        生成处理报告
        
        Returns:
            报告字符串
        """
        report = []
        report.append("=== SEM关键词分组处理报告 ===")
        report.append(f"原始关键词数量: {len(self.original_keywords)}")
        report.append(f"清洗后关键词数量: {len(self.cleaned_keywords)}")
        report.append(f"过滤后关键词数量: {len(self.filtered_keywords)}")
        report.append(f"生成分组数量: {len(self.groups)}")
        report.append("")
        
        report.append("分组详情:")
        for group_name, keywords in self.groups.items():
            report.append(f"  {group_name}: {len(keywords)} 个关键词")
        
        return "\n".join(report)
    
    def recommend_core_keyword(self) -> Dict[str, any]:
        """
        从所有关键词中提取核心品牌词
        
        算法逻辑：
        1. 统计所有关键词中出现频率最高的词根
        2. 选择最简洁的品牌词作为核心关键词
        
        Returns:
            包含推荐结果的字典
        """
        if not self.filtered_keywords:
            return {"error": "没有可用的关键词进行分析"}
        
        # 提取核心品牌词
        core_brand = self._extract_core_brand()
        
        if not core_brand:
            return {"error": "无法识别核心品牌词"}
        
        # 统计包含该品牌词的关键词数量
        brand_keywords = [kw for kw in self.filtered_keywords if core_brand.lower() in kw.lower()]
        
        result = {
            "recommended_keyword": core_brand,
            "analysis": {
                "brand_name": core_brand,
                "total_keywords": len(self.filtered_keywords),
                "brand_related_keywords": len(brand_keywords),
                "coverage_rate": round(len(brand_keywords) / len(self.filtered_keywords) * 100, 1),
                "recommendation_reason": f"'{core_brand}'是出现频率最高的核心品牌词，在{len(brand_keywords)}个关键词中出现"
            }
        }
        
        return result
    

    
    def _extract_core_brand(self) -> str:
        """
        从关键词列表中提取核心品牌词
        
        根据配置选择使用改进算法或传统算法
        """
        if self.use_advanced_brand_algorithm:
            return self._extract_core_brand_advanced()
        else:
            return self._extract_core_brand_legacy()
    
    def set_brand_algorithm(self, use_advanced: bool = True, use_ngram: bool = True):
        """
        设置品牌词识别算法
        
        Args:
            use_advanced: True使用改进算法，False使用传统算法
            use_ngram: True启用n-gram分析，False禁用
        """
        self.use_advanced_brand_algorithm = use_advanced
        self.use_ngram_analysis = use_ngram
    
    def _extract_core_brand_advanced(self) -> str:
        """
        改进的核心品牌词提取算法（集成n-gram分析）
        
        算法逻辑：
        1. 多层次分析：词组 -> 单词 -> 字符
        2. n-gram模式识别
        3. 语义完整性评估
        4. 品牌词特征识别
        5. 上下文相关性分析
        """
        if not self.filtered_keywords:
            return ""
        
        # 第一步：提取候选品牌词组（结合n-gram分析）
        candidate_phrases = self._extract_candidate_phrases_with_ngram()
        
        # 第二步：评估每个候选词组的品牌价值
        phrase_scores = self._evaluate_brand_phrases_with_ngram(candidate_phrases)
        
        # 保存分析结果用于调试
        self.brand_analysis_debug = {
            'candidates': candidate_phrases,
            'scores': phrase_scores,
            'algorithm': 'advanced_with_ngram' if self.use_ngram_analysis else 'advanced',
            'ngram_enabled': self.use_ngram_analysis
        }
        
        # 第三步：如果没有合适的词组，回退到单词分析
        if not phrase_scores:
            fallback_result = self._extract_core_brand_fallback()
            self.brand_analysis_debug['fallback_used'] = True
            self.brand_analysis_debug['result'] = fallback_result
            return fallback_result
        
        # 返回得分最高的品牌词
        best_phrase = max(phrase_scores.items(), key=lambda x: x[1])[0]
        self.brand_analysis_debug['result'] = best_phrase
        return best_phrase
    
    def _extract_ngrams(self, text: str, n: int = 2) -> List[str]:
        """
        提取文本中的n-gram组合
        
        Args:
            text: 输入文本
            n: n-gram的长度（2=bigram, 3=trigram等）
            
        Returns:
            n-gram列表
        """
        # 移除标点符号和特殊字符
        clean_text = re.sub(r'[^\w]', '', text)
        
        if len(clean_text) < n:
            return []
        
        ngrams = []
        for i in range(len(clean_text) - n + 1):
            ngram = clean_text[i:i+n]
            ngrams.append(ngram)
        
        return ngrams

    def _extract_word_ngrams(self, text: str, n: int = 2) -> List[str]:
        """
        基于分词结果提取词级别的n-gram组合
        
        Args:
            text: 输入文本
            n: n-gram的长度
            
        Returns:
            词级别n-gram列表
        """
        words = jieba.lcut(text)
        
        # 过滤掉长度过短的词
        words = [word for word in words if len(word) >= 1]
        
        if len(words) < n:
            return []
        
        word_ngrams = []
        for i in range(len(words) - n + 1):
            ngram = ''.join(words[i:i+n])
            word_ngrams.append(ngram)
        
        return word_ngrams

    def _analyze_ngrams(self, keywords: List[str], max_n: int = 3) -> Dict[str, Dict[str, int]]:
        """
        分析关键词列表中的n-gram模式
        
        Args:
            keywords: 关键词列表
            max_n: 最大n-gram长度
            
        Returns:
            各种n-gram的统计结果
        """
        ngram_stats = {}
        
        for n in range(2, max_n + 1):
            char_ngrams = Counter()
            word_ngrams = Counter()
            
            for keyword in keywords:
                # 字符级n-gram
                char_grams = self._extract_ngrams(keyword, n)
                for gram in char_grams:
                    char_ngrams[gram] += 1
                
                # 词级n-gram
                word_grams = self._extract_word_ngrams(keyword, n)
                for gram in word_grams:
                    word_ngrams[gram] += 1
            
            # 过滤低频n-gram
            min_freq = max(2, len(keywords) // 20)
            
            ngram_stats[f'{n}_gram_char'] = {
                gram: count for gram, count in char_ngrams.items() 
                if count >= min_freq and len(gram) >= 2
            }
            
            ngram_stats[f'{n}_gram_word'] = {
                gram: count for gram, count in word_ngrams.items() 
                if count >= min_freq and len(gram) >= 2
            }
        
        return ngram_stats

    def _is_meaningful_ngram(self, ngram: str) -> bool:
        """
        判断n-gram是否有意义
        
        Args:
            ngram: n-gram字符串
            
        Returns:
            是否有意义
        """
        # 无意义的模式
        meaningless_patterns = [
            r'^[0-9]+$',  # 纯数字
            r'^[a-zA-Z]+$',  # 纯英文字母
            r'^[^\u4e00-\u9fa5]+$',  # 不包含中文
        ]
        
        for pattern in meaningless_patterns:
            if re.match(pattern, ngram):
                return False
        
        # 常见的无意义词汇
        meaningless_words = {
            '的', '是', '在', '了', '和', '与', '或', '及', '等', '有', '无',
            '不', '很', '最', '更', '中', '上', '下', '前', '后', '左', '右'
        }
        
        # 如果完全由无意义词组成，则过滤
        if ngram in meaningless_words:
            return False
        
        return True

    def _extract_candidate_phrases(self) -> Dict[str, int]:
        """提取候选品牌词组"""
        phrase_counter = Counter()
        
        for keyword in self.filtered_keywords:
            # 使用jieba分词
            words = jieba.lcut(keyword)
            
            # 生成2-4字的词组组合
            for i in range(len(words)):
                for j in range(i + 1, min(i + 4, len(words) + 1)):
                    phrase = ''.join(words[i:j])
                    if 2 <= len(phrase) <= 6:  # 控制词组长度
                        phrase_counter[phrase] += 1
        
        # 过滤出现频率较低的词组
        min_frequency = max(2, len(self.filtered_keywords) // 10)
        return {phrase: count for phrase, count in phrase_counter.items() 
                if count >= min_frequency}
    
    def _extract_candidate_phrases_with_ngram(self) -> Dict[str, int]:
        """
        结合传统方法和n-gram分析提取候选品牌词组
        """
        # 传统方法提取的候选词组
        traditional_candidates = self._extract_candidate_phrases()
        
        # 如果禁用n-gram分析，直接返回传统结果
        if not self.use_ngram_analysis:
            return traditional_candidates
        
        # n-gram分析
        ngram_analysis = self._analyze_ngrams(self.filtered_keywords, max_n=3)
        
        # 合并候选词组
        all_candidates = Counter(traditional_candidates)
        
        # 从n-gram分析中提取高质量候选词组
        for ngram_type, ngram_data in ngram_analysis.items():
            if 'word' in ngram_type:  # 优先使用词级n-gram
                for ngram, count in ngram_data.items():
                    # 过滤条件：长度适中，频率足够
                    if 2 <= len(ngram) <= 6 and count >= 3:
                        # 检查n-gram是否包含有意义的内容
                        if self._is_meaningful_ngram(ngram):
                            # 给n-gram分析的结果适当加权
                            weight = 1.2 if '3_gram' in ngram_type else 1.0
                            all_candidates[ngram] += int(count * weight)
        
        # 过滤最终候选词组
        min_frequency = max(2, len(self.filtered_keywords) // 15)
        return {phrase: count for phrase, count in all_candidates.items() 
                if count >= min_frequency}
    
    def _evaluate_brand_phrases(self, candidates: Dict[str, int]) -> Dict[str, float]:
        """评估候选词组的品牌价值"""
        scores = {}
        
        # 功能词和通用词，降低品牌价值
        generic_words = {
            '软件', '工具', '程序', '应用', '系统', '平台', '网站', '官网',
            '下载', '安装', '版本', '最新', '免费', '中文', '官方', '正版',
            '怎么', '如何', '什么', '哪个', '好用', '安全', '教程', '使用',
            '播放', '视频', '音频', '图片', '文件', '管理', '编辑', '转换'
        }
        
        # 品牌特征词，提升品牌价值
        brand_indicators = {
            '输入法', '播放器', '浏览器', '杀毒', '压缩', '编辑器', '管家',
            '助手', '卫士', '大师', '专家', '王', '宝', '通', '云', '讯',
            '韩语', '中文', '英文', '日语', '搜狗', '百度', '腾讯', '阿里',
            '迅雷', '暴风', 'QQ', '微信', '360', '金山', '瑞星'
        }
        
        # 语言标识词，与软件类型结合时应获得更高权重
        language_identifiers = {'韩语', '日语', '中文', '英文', '法语', '德语', '俄语', '西班牙语'}
        
        # 软件类型词
        software_types = {'输入法', '播放器', '浏览器', '编辑器', '杀毒', '压缩'}
        
        for phrase, frequency in candidates.items():
            score = 0.0
            
            # 基础分数：出现频率
            score += frequency * 1.0
            
            # 长度奖励：2-4字的词组更可能是品牌词
            if 2 <= len(phrase) <= 4:
                score += 2.0
            elif len(phrase) == 5:
                score += 1.0
            
            # 品牌特征分析
            has_brand_indicator = any(indicator in phrase for indicator in brand_indicators)
            has_generic_word = any(generic in phrase for generic in generic_words)
            
            # 检查是否是语言+软件类型的组合（如"日语输入法"）
            has_language = any(lang in phrase for lang in language_identifiers)
            has_software_type = any(soft_type in phrase for soft_type in software_types)
            is_language_software_combo = has_language and has_software_type
            
            if is_language_software_combo:
                score += 10.0  # 语言+软件类型组合获得最高加分
            elif has_brand_indicator and not has_generic_word:
                score += 5.0  # 强品牌特征
            elif has_brand_indicator:
                score += 2.0  # 中等品牌特征
            elif has_generic_word:
                score -= 3.0  # 通用词减分
            
            # 覆盖率分析：在多少关键词中出现
            coverage = sum(1 for kw in self.filtered_keywords if phrase in kw)
            coverage_rate = coverage / len(self.filtered_keywords)
            
            if coverage_rate > 0.3:  # 高覆盖率
                score += 3.0
            elif coverage_rate > 0.1:  # 中等覆盖率
                score += 1.0
            
            # 独特性分析：避免过于通用的词
            if len(phrase) >= 3 and not has_generic_word:
                score += 1.0
            
            # 完整性奖励：优先选择更完整、更具体的品牌词
            if is_language_software_combo:
                # 语言+软件类型组合代表完整的品牌概念
                score += 3.0
                
                # 如果覆盖率也很高，额外奖励
                if coverage_rate > 0.5:
                    score += 2.0
            
            # 特殊加分：组合词（如"韩语输入法"）
            if len(phrase) >= 4 and has_brand_indicator:
                score += 2.0
            
            # 对于纯软件类型词（如"输入法"）在有更具体组合时降低优先级
            if phrase in software_types and len(phrase) <= 3:
                # 检查是否存在包含该软件类型的语言组合词
                language_combos = []
                for lang in language_identifiers:
                    combo1 = lang + phrase
                    combo2 = phrase + lang
                    if combo1 in candidates:
                        language_combos.append(combo1)
                    if combo2 in candidates:
                        language_combos.append(combo2)
                
                if language_combos:
                    # 如果存在语言组合词，大幅降低纯软件类型词的优先级
                    max_combo_freq = max(candidates[combo] for combo in language_combos)
                    if max_combo_freq >= frequency * 0.5:  # 组合词频率达到纯词频率的50%以上
                        score -= 8.0  # 大幅降低优先级
            
            scores[phrase] = score
        
        # 过滤掉得分过低的候选词
        return {phrase: score for phrase, score in scores.items() if score > 0}
    
    def _evaluate_brand_phrases_with_ngram(self, candidates: Dict[str, int]) -> Dict[str, float]:
        """
        评估候选词组的品牌价值（集成n-gram分析结果）
        """
        scores = {}
        
        # 功能词和通用词，降低品牌价值
        generic_words = {
            '软件', '工具', '程序', '应用', '系统', '平台', '网站', '官网',
            '下载', '安装', '版本', '最新', '免费', '中文', '官方', '正版',
            '怎么', '如何', '什么', '哪个', '好用', '安全', '教程', '使用',
            '播放', '视频', '音频', '图片', '文件', '管理', '编辑', '转换'
        }
        
        # 品牌特征词，提升品牌价值
        brand_indicators = {
            '输入法', '播放器', '浏览器', '杀毒', '压缩', '编辑器', '管家',
            '助手', '卫士', '大师', '专家', '王', '宝', '通', '云', '讯',
            '韩语', '中文', '英文', '日语', '搜狗', '百度', '腾讯', '阿里',
            '迅雷', '暴风', 'QQ', '微信', '360', '金山', '瑞星'
        }
        
        # 语言标识词，与软件类型结合时应获得更高权重
        language_identifiers = {'韩语', '日语', '中文', '英文', '法语', '德语', '俄语', '西班牙语'}
        
        # 软件类型词
        software_types = {'输入法', '播放器', '浏览器', '编辑器', '杀毒', '压缩'}
        
        # 如果启用了n-gram分析，获取n-gram统计
        ngram_analysis = None
        if self.use_ngram_analysis:
            ngram_analysis = self._analyze_ngrams(self.filtered_keywords, max_n=3)
        
        for phrase, frequency in candidates.items():
            score = 0.0
            
            # 基础分数：出现频率
            score += frequency * 1.0
            
            # 品牌特征分析（需要先计算，因为n-gram加分需要用到）
            has_brand_indicator = any(indicator in phrase for indicator in brand_indicators)
            has_generic_word = any(generic in phrase for generic in generic_words)
            
            # 检查是否是语言+软件类型的组合（如"日语输入法"）
            has_language = any(lang in phrase for lang in language_identifiers)
            has_software_type = any(soft_type in phrase for soft_type in software_types)
            is_language_software_combo = has_language and has_software_type
            
            # n-gram加分：如果词组在n-gram分析中也有高频出现
            if ngram_analysis and self.use_ngram_analysis:
                ngram_bonus = 0.0
                for ngram_type, ngram_data in ngram_analysis.items():
                    if phrase in ngram_data:
                        ngram_freq = ngram_data[phrase]
                        
                        # 计算基础n-gram权重
                        if '3_gram' in ngram_type:
                            base_bonus = ngram_freq * 1.5  # 3-gram权重更高
                        elif '2_gram' in ngram_type:
                            base_bonus = ngram_freq * 1.0
                        else:
                            base_bonus = 0
                        
                        # 对于语言+软件组合，保持原有加分
                        if is_language_software_combo:
                            ngram_bonus += base_bonus
                        else:
                            # 对于通用词，限制n-gram加分
                            coverage_rate = sum(1 for kw in self.filtered_keywords if phrase in kw) / len(self.filtered_keywords)
                            if coverage_rate > 0.9:
                                # 覆盖率过高的通用词，大幅限制n-gram加分
                                ngram_bonus += min(base_bonus * 0.1, 20.0)  # 最多20分
                            elif coverage_rate > 0.7:
                                # 覆盖率较高的词，适度限制n-gram加分
                                ngram_bonus += min(base_bonus * 0.3, 50.0)  # 最多50分
                            else:
                                # 覆盖率适中的词，保持原有加分
                                ngram_bonus += base_bonus
                
                score += ngram_bonus
            
            # 长度奖励：2-4字的词组更可能是品牌词
            if 2 <= len(phrase) <= 4:
                score += 2.0
                # n-gram分析中，完整词组应该获得更高奖励
                if self.use_ngram_analysis and len(phrase) >= 3:
                    score += 1.0
            elif len(phrase) == 5:
                score += 1.0
            
            # 品牌特征分析
            has_brand_indicator = any(indicator in phrase for indicator in brand_indicators)
            has_generic_word = any(generic in phrase for generic in generic_words)
            
            # 检查是否是语言+软件类型的组合（如"日语输入法"）
            has_language = any(lang in phrase for lang in language_identifiers)
            has_software_type = any(soft_type in phrase for soft_type in software_types)
            is_language_software_combo = has_language and has_software_type
            
            if is_language_software_combo:
                base_score = 80.0  # 大幅提高语言+软件类型组合的基础分数
                # 如果通过n-gram分析发现，额外加分
                if self.use_ngram_analysis:
                    base_score += 30.0  # 增加n-gram分析的额外加分
                score += base_score
            elif has_brand_indicator and not has_generic_word:
                score += 5.0  # 强品牌特征
            elif has_brand_indicator:
                score += 2.0  # 中等品牌特征
            elif has_generic_word:
                score -= 3.0  # 通用词减分
            
            # 覆盖率分析：在多少关键词中出现
            coverage = sum(1 for kw in self.filtered_keywords if phrase in kw)
            coverage_rate = coverage / len(self.filtered_keywords)
            
            # 重新设计覆盖率评分策略
            if is_language_software_combo:
                # 对于语言+软件组合，覆盖率加分更合理
                if coverage_rate > 0.5:  # 高覆盖率
                    score += 5.0
                elif coverage_rate > 0.3:  # 中等覆盖率
                    score += 3.0
                elif coverage_rate > 0.1:  # 低覆盖率
                    score += 1.0
            else:
                # 对于通用词，过高的覆盖率反而降低品牌价值
                if coverage_rate > 0.9:  # 覆盖率过高，可能是通用词
                    score -= 50.0  # 大幅减分，特别是对于纯软件类型词
                elif coverage_rate > 0.7:  # 覆盖率很高
                    score += 1.0  # 少量加分
                elif coverage_rate > 0.3:  # 中等覆盖率
                    score += 3.0
                elif coverage_rate > 0.1:  # 低覆盖率
                    score += 1.0
            
            # 独特性分析：避免过于通用的词
            if len(phrase) >= 3 and not has_generic_word:
                score += 1.0
            
            # 完整性奖励：优先选择更完整、更具体的品牌词
            if is_language_software_combo:
                # 语言+软件类型组合代表完整的品牌概念
                score += 20.0  # 增加完整性奖励
                
                # 如果覆盖率也很高，额外奖励
                if coverage_rate > 0.5:
                    score += 15.0  # 增加高覆盖率奖励
                
                # n-gram分析确认的完整组合，额外奖励
                if self.use_ngram_analysis:
                    score += 25.0  # 大幅增加n-gram分析确认的奖励
            
            # 特殊加分：组合词（如"韩语输入法"）
            if len(phrase) >= 4 and has_brand_indicator:
                score += 2.0
            
            # 对于纯软件类型词（如"输入法"）在有更具体组合时降低优先级
            if phrase in software_types and len(phrase) <= 3:
                # 检查是否存在包含该软件类型的语言组合词
                language_combos = []
                for lang in language_identifiers:
                    combo1 = lang + phrase
                    combo2 = phrase + lang
                    if combo1 in candidates:
                        language_combos.append(combo1)
                    if combo2 in candidates:
                        language_combos.append(combo2)
                
                if language_combos:
                    # 如果存在语言组合词，大幅降低纯软件类型词的优先级
                    max_combo_freq = max(candidates[combo] for combo in language_combos)
                    if max_combo_freq >= frequency * 0.3:  # 降低阈值，组合词频率达到纯词频率的30%以上就开始惩罚
                        penalty = 50.0  # 大幅增加惩罚力度
                        # 如果n-gram分析也支持这个决策，加大惩罚
                        if self.use_ngram_analysis:
                            penalty += 20.0  # 增加n-gram分析的惩罚
                        # 如果覆盖率过高，额外惩罚
                        if coverage_rate > 0.9:
                            penalty += 30.0  # 高覆盖率额外惩罚
                        score -= penalty
            
            scores[phrase] = score
        
        # 过滤掉得分过低的候选词
        return {phrase: score for phrase, score in scores.items() if score > 0}
    
    def _extract_core_brand_fallback(self) -> str:
        """回退到原始算法的简化版本"""
        word_counter = Counter()
        
        function_words = {
            '播放器', '软件', '工具', '程序', '应用', '下载', '安装', '版本',
            '最新', '官网', '免费', '中文', '绿色', '精简', '纯净', '完美',
            '怎么', '如何', '什么', '哪个', '比较', '对比', '安全', '好用'
        }
        
        for keyword in self.filtered_keywords:
            words = self.extract_core_words(keyword, min_word_length=2)
            for word in words:
                if word not in function_words and len(word) >= 2:
                    word_counter[word] += 1
        
        if word_counter:
            return word_counter.most_common(1)[0][0]
        return ""
    
    def _extract_core_brand_legacy(self) -> str:
        """
        原始的核心品牌词提取算法（保留作为备选）
        
        算法逻辑：
        1. 统计长度>=3的词根出现频率
        2. 过滤掉常见功能词
        3. 选择出现频率最高的品牌词
        """
        word_counter = Counter()
        
        # 常见功能词，需要过滤掉
        function_words = {
            '播放器', '软件', '工具', '程序', '应用', '下载', '安装', '版本',
            '最新', '官网', '免费', '中文', '绿色', '精简', '纯净', '完美',
            '怎么', '如何', '什么', '哪个', '比较', '对比', '安全', '好用'
        }
        
        for keyword in self.filtered_keywords:
            words = self.extract_core_words(keyword, min_word_length=3)
            for word in words:
                # 过滤功能词
                if word not in function_words:
                    word_counter.update([word])
        
        # 保存分析结果用于调试
        self.brand_analysis_debug = {
            'word_counter': dict(word_counter.most_common(10)),
            'algorithm': 'legacy'
        }
        
        # 返回最常见的词作为核心品牌词
        if word_counter:
            result = word_counter.most_common(1)[0][0]
            self.brand_analysis_debug['result'] = result
            return result
        
        self.brand_analysis_debug['result'] = ""
        return ""
    
    def export_dual_results(self, base_filename: str) -> Tuple[bool, str, str]:
        """
        导出两个不同的CSV文件：
        1. 完整的关键词分组文件
        2. 去重的推广计划文件（每个Ad Group只保留一个代表性关键词）
        
        Args:
            base_filename: 基础文件名（不含扩展名）
            
        Returns:
            (是否成功, 完整文件路径, 推广计划文件路径)
        """
        try:
            # 确保export目录存在
            import os
            export_dir = "export"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            # 生成文件路径
            full_file = os.path.join(export_dir, f"{base_filename}_完整分组.csv")
            plan_file = os.path.join(export_dir, f"{base_filename}_推广计划.csv")
            
            # 获取核心品牌词
            core_brand = self._extract_core_brand()
            campaign_name = f"第三方软件-{core_brand}" if core_brand else "第三方软件"
            
            # 1. 导出完整分组文件
            full_results = []
            
            # 首先添加核心品牌词组（如果存在）
            if core_brand:
                full_results.append({
                    'Campaign': campaign_name,
                    'Ad Group': '核心品牌词组',
                    'Keyword': core_brand,
                    'Original Keyword': core_brand
                })
            
            # 添加其他分组的所有关键词
            for group_name, group_keywords in self.groups.items():
                for keyword in group_keywords:
                    # 跳过核心品牌词，避免重复
                    if core_brand and keyword.lower() == core_brand.lower():
                        continue
                        
                    # 找到对应的原始关键词
                    original_keyword = ""
                    for orig in self.original_keywords:
                        if orig.strip().lower() == keyword:
                            original_keyword = orig
                            break
                    
                    full_results.append({
                        'Campaign': campaign_name,
                        'Ad Group': group_name,
                        'Keyword': keyword,
                        'Original Keyword': original_keyword or keyword
                    })
            
            # 保存完整分组文件
            full_df = pd.DataFrame(full_results)
            full_df.to_csv(full_file, index=False, encoding='utf-8-sig')
            
            # 2. 导出推广计划文件（去重版本）
            plan_results = []
            
            # 核心品牌词组
            if core_brand:
                plan_results.append({
                    'Campaign': campaign_name,
                    'Ad Group': '核心品牌词组',
                    'Keyword': core_brand,
                    'Original Keyword': core_brand,
                    'Match Type': '精确匹配',
                    'Bid Strategy': '最高出价',
                    'Keywords Count': 1
                })
            
            # 每个分组只选择一个代表性关键词
            for group_name, group_keywords in self.groups.items():
                if not group_keywords:
                    continue
                
                # 选择代表性关键词的策略：
                # 1. 优先选择最短的关键词（通常更核心）
                # 2. 如果长度相同，选择字母序最小的
                filtered_keywords = [kw for kw in group_keywords 
                                   if not (core_brand and kw.lower() == core_brand.lower())]
                
                if not filtered_keywords:
                    continue
                
                representative_keyword = min(filtered_keywords, 
                                           key=lambda x: (len(x), x.lower()))
                
                # 找到对应的原始关键词
                original_keyword = ""
                for orig in self.original_keywords:
                    if orig.strip().lower() == representative_keyword:
                        original_keyword = orig
                        break
                
                # 根据分组类型推荐匹配方式和出价策略
                match_type, bid_strategy = self._get_match_strategy(group_name)
                
                plan_results.append({
                    'Campaign': campaign_name,
                    'Ad Group': group_name,
                    'Keyword': representative_keyword,
                    'Original Keyword': original_keyword or representative_keyword,
                    'Match Type': match_type,
                    'Bid Strategy': bid_strategy,
                    'Keywords Count': len(filtered_keywords)
                })
            
            # 保存推广计划文件
            plan_df = pd.DataFrame(plan_results)
            plan_df.to_csv(plan_file, index=False, encoding='utf-8-sig')
            
            print(f"📁 完整分组文件已导出到: {full_file}")
            print(f"📋 推广计划文件已导出到: {plan_file}")
            
            return True, full_file, plan_file
            
        except Exception as e:
            print(f"导出结果失败: {e}")
            return False, "", ""
    
    def _get_match_strategy(self, group_name: str) -> Tuple[str, str]:
        """
        根据分组类型推荐匹配方式和出价策略
        
        Args:
            group_name: 分组名称
            
        Returns:
            (匹配方式, 出价策略)
        """
        strategies = {
            '核心品牌词组': ('精确匹配', '最高出价'),
            '域名词': ('精确匹配', '高出价'),
            '下载词': ('广泛匹配', '中等出价'),
            '疑问词': ('短语匹配', '低出价'),
            '对比词': ('精确匹配', '高出价'),
            '平台词': ('短语匹配', '中等出价'),
            '版本词': ('广泛匹配', '中等出价'),
            '功能词': ('短语匹配', '低出价')
        }
        
        return strategies.get(group_name, ('广泛匹配', '中等出价'))
    
    def convert_baidu_to_bing(self, input_file: str, output_file: str = None) -> bool:
        """
        将百度SEM关键词文件转换为Bing Ads格式
        
        Args:
            input_file: 百度关键词文件路径
            output_file: 输出文件路径（可选）
            
        Returns:
            转换是否成功
        """
        try:
            return self.baidu_to_bing_converter.convert_file(input_file, output_file)
        except Exception as e:
            print(f"百度转Bing转换失败: {e}")
            return False
    
    def convert_baidu_to_bing_from_upload(self, uploaded_file, output_filename: str = None) -> Optional[str]:
        """
        从上传文件转换百度格式到Bing格式
        
        Args:
            uploaded_file: 上传的文件对象
            output_filename: 输出文件名（可选）
            
        Returns:
            输出文件路径或None
        """
        try:
            return self.baidu_to_bing_converter.convert_uploaded_file(uploaded_file, output_filename)
        except Exception as e:
            print(f"上传文件百度转Bing转换失败: {e}")
            return None
    
    def convert_baidu_to_bing_with_options(self, uploaded_file, output_filename: str = None, 
                                         enable_url_parsing: bool = True, 
                                         enable_data_cleaning: bool = True, 
                                         preserve_original_data: bool = False) -> Optional[pd.DataFrame]:
        """
        从上传文件转换百度格式到Bing格式，严格按照原先代码逻辑
        
        Args:
            uploaded_file: 上传的文件对象
            output_filename: 输出文件名（可选）
            enable_url_parsing: 是否启用URL解析
            enable_data_cleaning: 是否启用数据清洗
            preserve_original_data: 是否保留原始数据列
            
        Returns:
            转换后的DataFrame或None
        """
        try:
            # 直接调用转换器的方法
            return self.baidu_to_bing_converter.convert_uploaded_file(
                uploaded_file,
                enable_url_parsing=enable_url_parsing,
                enable_data_cleaning=enable_data_cleaning,
                preserve_original_data=preserve_original_data
            )
            
        except Exception as e:
            print(f"转换过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def get_baidu_template_info(self) -> Dict[str, str]:
        """
        获取百度转Bing模板信息
        
        Returns:
            模板信息字典
        """
        return {
            'upload_template': self.baidu_to_bing_converter.upload_template_path,
            'export_template': self.baidu_to_bing_converter.export_template_path
        }



def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SEM关键词智能分组工具 - MVP版本')
    parser.add_argument('input_file', help='输入关键词文件路径 (.txt, .csv, .xlsx)')
    parser.add_argument('-o', '--output', default='grouped_keywords.csv', 
                       help='输出文件路径 (默认: grouped_keywords.csv)')
    parser.add_argument('-n', '--negative', help='否定词文件路径 (.txt)')
    parser.add_argument('-c', '--column', help='CSV文件中关键词列名')
    parser.add_argument('-m', '--min-group-size', type=int, default=3,
                       help='最小分组大小 (默认: 3)')
    parser.add_argument('--recommend-core', action='store_true', 
                       help='推荐核心关键词')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        sys.exit(1)
    
    # 创建分组器
    grouper = KeywordGrouper(min_group_size=args.min_group_size)
    
    # 加载关键词
    keywords = grouper.load_keywords_from_file(args.input_file, args.column)
    if not keywords:
        print("错误: 无法加载关键词")
        sys.exit(1)
    
    # 加载否定词（如果提供）
    if args.negative:
        if os.path.exists(args.negative):
            grouper.load_negative_words(args.negative)
        else:
            print(f"警告: 否定词文件 '{args.negative}' 不存在，跳过否定词过滤")
    
    # 处理流程
    print("\n开始处理...")
    
    # 1. 清洗关键词
    cleaned_keywords = grouper.clean_keywords(keywords)
    
    # 2. 过滤关键词
    filtered_keywords, removed_keywords = grouper.filter_keywords(cleaned_keywords)
    
    # 3. 分组
    groups = grouper.group_by_common_words(filtered_keywords)
    
    # 4. 导出结果
    if grouper.export_results(args.output):
        print(f"\n{grouper.generate_report()}")
        
        # 5. 推荐核心关键词（如果指定）
        if args.recommend_core:
            print("\n" + "="*50)
            print("核心关键词推荐分析")
            print("="*50)
            
            recommendation = grouper.recommend_core_keyword()
            if "error" in recommendation:
                print(f"推荐失败: {recommendation['error']}")
            else:
                print(f"🎯 核心品牌词: {recommendation['recommended_keyword']}")
                print(f"📊 关键词覆盖率: {recommendation['analysis']['coverage_rate']}%")
                print(f"🏷️  推荐理由: {recommendation['analysis']['recommendation_reason']}")
                print(f"📈 相关关键词数量: {recommendation['analysis']['brand_related_keywords']}/{recommendation['analysis']['total_keywords']}")
        
        print(f"\n处理完成！结果已保存到 '{args.output}'")
    else:
        print("导出失败")
        sys.exit(1)


if __name__ == "__main__":
    main() 