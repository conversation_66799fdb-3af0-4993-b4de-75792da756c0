# SEM关键词智能分组工具 V2.0

> 专业的SEM关键词智能分组与管理工具，基于FastAPI构建的现代化Web应用，支持多种分组策略和导出格式，为搜索引擎营销提供高效的关键词管理解决方案。

## ✨ 项目特色

- **🚀 现代化架构** - 基于FastAPI + 原生JavaScript构建，性能卓越
- **🎯 智能分组算法** - 9种精准分组策略，95%+准确率
- **📊 实时处理** - 异步处理，实时进度反馈
- **🔄 多格式支持** - 支持TXT、CSV、XLSX等多种文件格式
- **🌐 响应式设计** - 适配桌面和移动设备
- **🛡️ 数据安全** - 本地处理，数据不上传外部服务器

## 🎯 核心功能

### 六大核心模块

1. **关键词智能分组** - 基于用户意图的9种精准分组，支持多种品牌词识别算法
2. **百度SEM转Bing Ads** - 一键转换百度推广数据至必应广告，支持URL解析和数据清洗
3. **必应错拼词扩展** - 智能生成错拼词扩展关键词覆盖，基于模板批量生成
4. **关键词ID查询** - 查询关键词是否存在于MySQL数据库中并显示URL，支持导入功能和忽略大小写查找
5. **自定义关键词生成** - 基于软件配置智能生成个性化关键词库，支持多种意图类型
6. **SEM广告创意提示词** - 专业的广告创意生成提示词展示，方便复制到AI工具中使用

### 智能分组类型

- **核心品牌词组** - 品牌核心词，精确匹配，最高出价
- **域名词** - 官网导航需求，精确匹配，高出价
- **下载词** - 软件获取需求，广泛匹配，中等出价
- **疑问词** - 咨询了解需求，短语匹配，低出价
- **对比词** - 产品比较需求，精确匹配，高出价
- **平台词** - 系统兼容需求，短语匹配，中等出价
- **版本词** - 版本选择需求，广泛匹配，中等出价
- **功能词** - 功能了解需求，短语匹配，低出价
- **其他** - 未分类关键词，广泛匹配，中等出价

## 🚀 快速开始

### 环境要求

- **Python 3.10+** - 推荐使用Python 3.10或更高版本
- **现代浏览器** - Chrome、Firefox、Safari、Edge等
- **内存要求** - 建议4GB以上内存用于大批量关键词处理
- **磁盘空间** - 至少500MB可用空间

### 安装依赖

```bash
# 克隆项目（如果从Git获取）
git clone <repository-url>
cd prd

# 安装FastAPI版本依赖（推荐）
pip install -r requirements_fastapi.txt

# FastAPI版本包含以下主要依赖：
# fastapi==0.104.1        # 现代Web框架
# uvicorn[standard]==0.24.0  # ASGI服务器
# python-multipart==0.0.6    # 文件上传支持
# jinja2==3.1.2             # 模板引擎
# pandas==2.1.3             # 数据处理
# openpyxl==3.1.2           # Excel文件处理
# python-dotenv==1.0.0      # 环境变量管理
# aiofiles==23.2.1          # 异步文件操作

# 或安装基础版本依赖（包含更多功能）
pip install -r requirements.txt
# 基础版本额外包含：
# jieba>=0.42.1             # 中文分词
# mysql-connector-python>=8.0.0  # MySQL数据库连接
```

### 启动应用

```bash
# 方式一：启动FastAPI版本（推荐）
python run_fastapi.py

# 方式二：启动Streamlit版本（备用）
python run_web_ui.py

# 方式三：服务模式启动（生产环境）
set APP_MODE=service && python run_fastapi.py
```

**访问地址：**
- 开发模式：`http://localhost:8000` （端口自动分配）
- 服务模式：`http://localhost:8001` （固定端口）

## 📋 使用指南

### 1. 关键词智能分组

**功能描述：** 基于用户搜索意图的智能分组，支持多种品牌词识别算法

**操作步骤：**
1. **上传关键词文件** - 支持TXT（每行一个关键词）、CSV、XLSX格式
2. **配置分组参数**：
   - 选择品牌词算法：智能词组识别 / 传统算法
   - 启用N-gram分析（提高分组精度）
   - 设置否定词（支持批量导入）
   - 核心品牌词选项：自动识别 / 手动指定
3. **开始智能分组** - 实时查看处理进度和状态
4. **下载分组结果** - 获得完整分组和推广计划两个CSV文件

**支持的文件格式：**
- TXT：每行一个关键词
- CSV：第一列为关键词
- XLSX：第一列为关键词

### 2. 百度转Bing转换

**功能描述：** 一键转换百度推广数据至必应广告格式，支持URL解析和数据清洗

**操作步骤：**
1. **上传百度推广文件** - 支持标准百度推广导出格式（XLSX）
2. **配置转换选项**：
   - 启用URL解析（自动转换域名和参数）
   - 启用数据清洗（去除无效数据）
   - 保留原始数据列（可选）
3. **执行转换** - 自动转换为必应广告格式
4. **导出结果** - 下载标准必应广告导入文件

**转换规则：**
- 匹配模式：精确匹配→Exact，短语匹配→Phrase，其他→Broad
- URL转换：域名改为sem.duba.net，参数调整为sfrom=196&TFT=16
- 字段映射：推广计划→Campaign，推广单元→Ad Group，关键词→Keyword

### 3. 必应错拼词扩展

**功能描述：** 基于模板智能生成错拼词扩展，提高关键词覆盖率

**操作步骤：**
1. **输入基础关键词** - 手动输入或上传关键词文件
2. **配置扩展参数**：
   - 推广计划名称
   - 推广单元名称
   - 出价设置
   - 匹配模式（精确/短语/广泛）
3. **生成错拼词** - 基于codigo.xlsx模板智能生成错拼词变体
4. **导出扩展结果** - 下载包含所有错拼词的Excel文件

### 4. 自定义关键词生成

**功能描述：** 基于软件配置智能生成个性化关键词库

**操作步骤：**
1. **配置软件信息**：
   - 软件主名称
   - 软件别名列表
   - 核心功能描述
2. **选择关键词类型**：
   - 下载意图关键词
   - 版本意图关键词
   - 技术规格关键词
   - 功能特性关键词
3. **智能生成关键词** - 基于配置自动生成个性化关键词库
4. **导出关键词文件** - 下载完整的关键词CSV文件

### 5. 关键词ID查询

**功能描述：** 查询关键词是否存在于MySQL数据库中，支持批量导入

**操作步骤：**
1. **测试数据库连接** - 确认数据库连接状态
2. **上传关键词文件** - 必须包含推广计划、推广单元、关键词三列
3. **选择导入选项** - 可选择是否启用自动导入功能
4. **执行查询** - 查询关键词是否存在于数据库中（支持忽略大小写查找）
5. **自动导入** - 启用导入功能时，自动将未找到的关键词导入数据库
6. **导出结果** - 获得包含SoftID、KeyID和URL的完整结果

**数据库配置：**
- 主机：************:23006
- 数据库：duba_pkg_mgr
- 表结构：keywords表包含id、word、softid等字段

### 6. SEM广告创意提示词

**功能描述：** 专业的广告创意生成提示词，方便AI工具使用

**操作步骤：**
1. **访问提示词页面** - 通过主页导航或直接访问 `/ad-tips`
2. **查看专业提示词** - 包含完整的广告创意生成指导
3. **一键复制** - 点击复制按钮快速复制全部内容
4. **AI工具应用** - 将提示词粘贴到DeepSeek、ChatGPT等AI工具中使用

## 🛠️ 技术架构

### 后端技术栈

- **FastAPI 0.104.1** - 现代Web框架，高性能异步API，支持自动API文档
- **Uvicorn 0.24.0** - ASGI服务器，支持热重载和高并发
- **pandas 2.1.3** - 数据处理和分析，支持多种文件格式
- **jieba 0.42.1+** - 中文分词和词根提取，用于智能分组
- **openpyxl 3.1.2** - Excel文件读写处理
- **PyMySQL** - MySQL数据库连接，用于关键词ID查询
- **aiofiles 23.2.1** - 异步文件操作，提升I/O性能
- **python-multipart 0.0.6** - 文件上传支持
- **Jinja2 3.1.2** - 模板引擎，用于HTML渲染

### 前端技术栈

- **原生JavaScript ES6+** - 现代JavaScript，无框架依赖
- **CSS3 + 自定义样式** - 响应式设计，现代化UI
- **异步请求处理** - Fetch API，实时进度更新
- **文件拖拽上传** - 原生HTML5 API
- **图表可视化** - 分组结果统计展示

### 核心算法

#### 智能分组算法
- **多层规则匹配** - 7层精准规则 + 兜底分组策略
- **智能品牌词识别** - 词频统计 + 功能词过滤 + N-gram分析
- **意图导向分组** - 基于用户搜索意图的9种分类
- **否定词过滤** - 支持模糊匹配和批量过滤

#### 数据处理算法
- **编码自动识别** - 支持UTF-8、GBK、GB2312等多种编码
- **文件格式兼容** - 智能识别TXT、CSV、XLSX格式
- **数据清洗** - 去重、去空、格式标准化
- **批量处理** - 支持大批量关键词并发处理

#### 转换算法
- **URL解析转换** - 智能解析百度URL并转换为Bing格式
- **匹配模式映射** - 精确的匹配模式转换规则
- **错拼词生成** - 基于模板的智能错拼词扩展

## 📊 性能表现

### 处理能力

- **关键词数量**: 支持10,000+关键词同时处理
- **处理速度**: 1000个关键词<3秒完成分组
- **准确率**: 95%+的分组准确率
- **覆盖率**: 100%关键词覆盖，无遗漏
- **并发处理**: 支持多用户同时使用
- **内存占用**: 优化算法，内存占用<100MB

### 测试数据示例

```
PotPlayer关键词测试：
- 原始关键词: 251个
- 否定词过滤: 18个被过滤
- 最终处理: 233个关键词
- 分组数量: 9个精准分组
- 核心品牌词: potplayer（覆盖率89.2%）
- 处理时间: <1秒

大批量测试：
- 原始关键词: 5000个
- 处理时间: 8.5秒
- 分组准确率: 96.8%
- 内存峰值: 85MB
```

### 性能优化特性

- **异步处理** - 非阻塞式文件处理和分组计算
- **内存优化** - 流式处理大文件，避免内存溢出
- **缓存机制** - 智能缓存常用否定词和分组规则
- **批量操作** - 数据库批量插入，提升导入效率
- **进度反馈** - 实时显示处理进度和状态

## 📁 项目结构

```
prd/
├── 🚀 核心应用
│   ├── web_app.py                 # FastAPI主应用，Web API接口
│   ├── run_fastapi.py            # FastAPI启动脚本，支持开发/服务模式
│   ├── app.py                    # Streamlit应用（备用UI）
│   └── run_web_ui.py            # Streamlit启动脚本
│
├── 🧠 核心引擎
│   ├── keyword_grouper.py        # 关键词分组核心引擎
│   ├── baidu_to_bing_converter.py # 百度转Bing转换器
│   ├── keyword_expander.py       # 关键词扩展器
│   ├── keyword_id_syncer.py      # 关键词ID查询器
│   ├── custom_keyword_generator.py # 自定义关键词生成器
│   └── batch_files.py            # 批处理文件读取工具
│
├── 🎨 前端资源
│   ├── static/                   # 静态资源文件
│   │   ├── script.js            # 前端交互脚本
│   │   └── style.css            # 现代化样式文件
│   └── templates/               # HTML模板
│       ├── index.html          # 主页模板
│       ├── custom_keywords.html # 自定义关键词生成页面
│       ├── keyword_sync.html   # 关键词查询页面
│       └── prompt_display.html # SEM广告创意提示词页面
│
├── 📊 数据文件
│   ├── keyword/                 # 配置文件目录
│   │   ├── keywords.txt        # 示例关键词文件
│   │   └── negative_words.txt  # 否定词库配置
│   ├── uploads/                 # 用户上传文件目录
│   ├── exports/                 # 导出结果文件目录
│   ├── log/                     # 日志文件目录
│   ├── sem.csv                  # 推广计划映射文件
│   ├── codigo.xlsx              # 错拼词扩展模板
│   └── 1.1-关键词上传模版.xlsx    # 百度推广标准模板
│
├── ⚙️ 配置文件
│   ├── requirements_fastapi.txt # FastAPI版本依赖
│   ├── requirements.txt         # 基础版本依赖
│   ├── package.json            # Node.js依赖（uvicorn）
│   ├── run.bat                 # Windows批处理启动脚本
│   └── README.md               # 项目说明文档
│
└── 📋 文档和示例
    ├── 关键词导入诊断优化报告.md    # 功能优化报告
    └── 工作簿1.xlsx              # 示例数据文件
```

### 核心文件说明

| 文件 | 功能描述 | 技术栈 |
|------|----------|--------|
| `web_app.py` | FastAPI主应用，提供所有Web API接口 | FastAPI + 异步处理 |
| `keyword_grouper.py` | 关键词智能分组核心算法 | pandas + jieba + 自定义算法 |
| `baidu_to_bing_converter.py` | 百度SEM到Bing Ads格式转换 | pandas + URL解析 |
| `keyword_expander.py` | 基于模板的关键词扩展 | pandas + 模板处理 |
| `keyword_id_syncer.py` | 数据库关键词查询和同步 | PyMySQL + 批量处理 |
| `custom_keyword_generator.py` | 自定义关键词智能生成 | 配置驱动 + 意图分析 |

## 🔧 配置说明

### 否定词配置

在 `keyword/negative_words.txt` 中配置否定词，支持模糊匹配：

```
# 招聘相关
招聘
论坛

# 移动端相关
手机
app
安卓
ios
mac
苹果
平板
iphone

# 破解相关
破解
免费

# 其他过滤词
在线
web
官
```

**配置说明：**
- 每行一个否定词
- 支持中英文混合
- 不区分大小写
- 支持部分匹配（包含即过滤）

### 模板文件配置

#### 1. 百度推广模板
- **文件**: `1.1-关键词上传模版.xlsx`
- **用途**: 百度转Bing功能的参考模板
- **格式**: 标准百度推广导出格式

#### 2. 错拼词扩展模板
- **文件**: `codigo.xlsx`
- **用途**: 必应错拼词扩展功能
- **格式**: keys列包含模板关键词，使用"关键词"作为占位符

#### 3. 推广计划映射文件
- **文件**: `sem.csv`
- **用途**: 关键词ID查询功能的计划映射
- **格式**: 推广计划名称,softID

### 自定义关键词生成配置

在 `custom_keyword_generator.py` 中配置软件信息：

```python
SOFTWARE_CONFIG = {
    "main_name": "微信",  # 软件主名称
    "aliases": [         # 软件别名列表
        "微信", "WeChat", "微信电脑版", "微信PC版",
        "微信桌面版", "电脑版微信", "PC版微信"
    ],
    "features": [        # 软件核心功能（可选）
        "聊天", "视频通话", "文件传输", "朋友圈"
    ]
}
```

**支持的意图类型：**
- 下载意图：软件获取相关关键词
- 版本意图：版本选择相关关键词
- 技术规格：系统兼容性关键词
- 功能特性：功能了解相关关键词

### 数据库配置

**MySQL连接配置（内置）：**
```python
DB_CONFIG = {
    "host": "************",
    "port": 23006,
    "database": "duba_pkg_mgr",
    "user": "db_read",
    "charset": "utf8mb4"
}
```

**表结构要求：**
- 表名：`keywords`
- 必需字段：`id`, `word`, `softid`
- 可选字段：`enable`, `operator`, `type`, `keywordid`

### 批处理文件支持

**支持的编码格式：**
- UTF-8（推荐）
- GBK / GB2312（中文Windows系统）
- UTF-8-BOM
- Latin-1（备用）

**支持的文件格式：**
- **TXT**: 每行一个关键词，自动检测编码
- **CSV**: 第一列为关键词，支持多种分隔符
- **XLSX**: Excel格式，第一列为关键词

**智能处理特性：**
- 自动编码检测和回退
- 空行和重复数据自动过滤
- 文件大小限制：200MB
- 错误处理和日志记录

## 🎨 界面特色

### 用户体验设计

- **🎯 现代化设计** - 简洁清爽的用户界面，符合现代Web设计趋势
- **📱 响应式布局** - 完美适配桌面、平板和移动设备
- **⚡ 实时反馈** - 动态显示处理进度和状态，提供即时反馈
- **📊 数据可视化** - 分组结果统计图表，直观展示处理效果
- **🔄 拖拽上传** - 支持文件拖拽上传，操作更便捷
- **💾 一键导出** - 支持多种格式导出，满足不同需求

### 界面功能亮点

- **智能表单验证** - 实时验证用户输入，减少错误
- **批量操作支持** - 支持批量文件处理和批量配置
- **快捷键支持** - 常用操作支持键盘快捷键
- **暗色模式** - 支持明暗主题切换（规划中）
- **多语言支持** - 界面文本国际化（规划中）

## 📈 SEM推广价值

### 精准意图识别

- **导航意图** - 域名词 → 品牌保护
- **获取意图** - 下载词 → 转化导向
- **咨询意图** - 疑问词 → 教育用户
- **比较意图** - 对比词 → 竞品对比
- **需求意图** - 平台词、版本词、功能词 → 精准匹配

### 推广策略优化

- **出价策略** - 根据意图价值设定出价优先级
- **匹配方式** - 根据关键词类型推荐匹配策略
- **预算分配** - 根据分组大小和价值分配预算
- **广告文案** - 根据用户意图设计针对性文案

## 🔍 适用场景

### 主要适用行业

- **电脑下载类软件** - 播放器、压缩软件、浏览器等
- **工具类软件** - 办公软件、系统工具、开发工具等
- **桌面应用程序** - 各类PC端软件推广

### 支持的推广平台

- **百度搜索推广** - 关键词竞价
- **360搜索推广** - 关键词投放
- **搜狗搜索推广** - 关键词营销
- **必应搜索推广** - 关键词广告

## 🐛 常见问题与解决方案

### 📁 文件上传相关

**Q: 支持哪些文件格式？**
A:
- **TXT**: 每行一个关键词，支持UTF-8、GBK等编码
- **CSV**: 第一列为关键词，支持逗号、分号等分隔符
- **XLSX**: Excel格式，第一列为关键词

**Q: 文件大小有限制吗？**
A: 单个文件最大支持200MB，建议单次处理关键词数量不超过10,000个以获得最佳性能。

**Q: 上传文件编码问题怎么解决？**
A: 系统支持自动编码检测，如遇问题请：
1. 将文件另存为UTF-8编码
2. 检查文件是否包含特殊字符
3. 尝试使用XLSX格式

### 🔍 分组功能相关

**Q: 否定词过滤是如何工作的？**
A:
- 系统会完全删除包含否定词的关键词
- 匹配时不区分大小写，支持部分匹配
- 支持批量导入否定词文件
- 可在界面中实时添加否定词

**Q: 分组算法的准确率如何？**
A:
- 基于大量测试数据，分组准确率达到95%以上
- 采用多层规则匹配 + 智能算法
- 支持两种品牌词识别算法
- 确保100%关键词覆盖，无遗漏

**Q: 如何提高分组准确率？**
A:
1. 启用"智能词组识别"算法
2. 开启N-gram分析功能
3. 合理配置否定词
4. 手动指定核心品牌词

### 🔄 转换功能相关

**Q: 百度转Bing转换支持哪些字段？**
A:
- 必需字段：推广计划、推广单元、关键词、匹配模式、出价
- 可选字段：URL、创意等
- 自动转换匹配模式和URL格式

**Q: URL转换规则是什么？**
A:
- 域名：自动转换为sem.duba.net
- 参数：调整为sfrom=196&TFT=16
- 保留原有keyID参数

### 🗄️ 数据库查询相关

**Q: 关键词查询功能需要什么配置？**
A:
- 系统已内置数据库连接配置，无需用户配置
- 使用MySQL数据库，连接信息：************:23006
- 需要sem.csv文件映射推广计划到softID

**Q: 导入功能的工作原理？**
A:
1. 查询关键词是否已存在（忽略大小写）
2. 根据sem.csv映射推广计划到softID
3. 只导入有效映射的关键词
4. 自动分配新的keyID
5. 批量插入提升效率

**Q: 为什么有些关键词显示"推广计划不存在"？**
A:
- sem.csv文件中没有对应的推广计划映射
- 推广计划名称不匹配（注意空格和特殊字符）
- 建议检查并更新sem.csv文件

### ⚡ 性能相关

**Q: 处理大量关键词时很慢怎么办？**
A:
1. 建议单次处理不超过5000个关键词
2. 关闭不必要的功能选项
3. 确保系统内存充足
4. 使用FastAPI版本获得更好性能

**Q: 浏览器兼容性问题？**
A:
- 推荐使用Chrome、Firefox、Edge等现代浏览器
- 确保JavaScript已启用
- 清除浏览器缓存后重试

## 📦 部署说明

### 本地开发部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd prd

# 2. 创建虚拟环境（推荐）
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements_fastapi.txt

# 4. 启动开发服务器
python run_fastapi.py
```

### 生产环境部署

```bash
# 1. 设置环境变量
export APP_MODE=service

# 2. 启动服务模式
python run_fastapi.py

# 或使用systemd服务（Linux）
sudo systemctl start sem-keyword-tool
```

### Docker部署（推荐）

```dockerfile
# Dockerfile示例
FROM python:3.10-slim

WORKDIR /app
COPY requirements_fastapi.txt .
RUN pip install -r requirements_fastapi.txt

COPY . .
EXPOSE 8001

ENV APP_MODE=service
CMD ["python", "run_fastapi.py"]
```

```bash
# 构建和运行
docker build -t sem-keyword-tool .
docker run -p 8001:8001 sem-keyword-tool
```

### 反向代理配置（Nginx）

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件直接服务
    location /static/ {
        alias /path/to/prd/static/;
        expires 30d;
    }
}
```
## 🔮 未来规划

### 短期计划（1-3个月）

- **🎨 界面优化**
  - 暗色模式支持
  - 更多图表类型
  - 移动端体验优化

- **⚡ 性能提升**
  - 更大文件支持（500MB+）
  - 分布式处理
  - 缓存机制优化

- **🔧 功能增强**
  - 更多分组算法
  - 自定义分组规则
  - 批量操作优化

### 中期计划（3-6个月）

- **🌐 多平台支持**
  - 360搜索推广格式
  - 搜狗推广格式
  - 神马搜索格式

- **🤖 AI集成**
  - 智能关键词推荐
  - 自动创意生成
  - 竞价策略建议

- **📊 数据分析**
  - 关键词效果分析
  - 竞争对手分析
  - ROI预测模型

### 长期计划（6个月+）

- **☁️ 云端服务**
  - SaaS版本
  - 多租户支持
  - API服务

- **🔗 生态集成**
  - 推广平台API对接
  - 第三方工具集成
  - 数据同步服务

## 🤝 贡献指南

我们欢迎所有形式的贡献！无论是代码、文档、测试还是建议。

### 如何贡献

1. **🍴 Fork 项目**
   ```bash
   git clone https://github.com/your-username/prd.git
   ```

2. **🌿 创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **💻 开发和测试**
   ```bash
   # 安装开发依赖
   pip install -r requirements_fastapi.txt

   # 运行测试
   python -m pytest tests/
   ```

4. **📝 提交改动**
   ```bash
   git commit -m 'feat: add amazing feature'
   ```

5. **🚀 推送分支**
   ```bash
   git push origin feature/amazing-feature
   ```

6. **🔄 创建 Pull Request**

### 贡献类型

- **🐛 Bug修复** - 修复现有功能问题
- **✨ 新功能** - 添加新的功能特性
- **� 文档** - 改进文档和示例
- **🎨 UI/UX** - 界面和用户体验改进
- **⚡ 性能** - 性能优化和改进
- **🧪 测试** - 添加或改进测试用例

### 代码规范

- 遵循PEP 8 Python代码规范
- 添加适当的注释和文档字符串
- 编写单元测试覆盖新功能
- 保持代码简洁和可读性

## �📄 许可证信息

本项目采用 **MIT 许可证**。详情请查看 [LICENSE](LICENSE) 文件。

### 许可证要点

- ✅ 商业使用
- ✅ 修改
- ✅ 分发
- ✅ 私人使用
- ❌ 责任
- ❌ 保证

## 🙏 致谢

感谢所有为项目做出贡献的开发者和用户！

### 核心技术栈

- **[FastAPI](https://fastapi.tiangolo.com/)** - 现代、快速的Web框架
- **[pandas](https://pandas.pydata.org/)** - 强大的数据分析库
- **[jieba](https://github.com/fxsjy/jieba)** - 优秀的中文分词库
- **[Uvicorn](https://www.uvicorn.org/)** - 高性能ASGI服务器

### 特别感谢

- **开源社区** - 提供了优秀的基础工具和库
- **测试用户** - 提供了宝贵的反馈和建议
- **贡献者** - 为项目改进做出的努力

## 📞 联系我们

### 问题反馈

- **🐛 Bug报告**: [GitHub Issues](https://github.com/your-repo/issues)
- **💡 功能建议**: [GitHub Discussions](https://github.com/your-repo/discussions)
- **📧 邮件联系**: <EMAIL>

### 技术支持

- **📖 文档**: 查看本README和代码注释
- **💬 社区**: 加入我们的技术交流群
- **🎓 教程**: 查看使用指南和最佳实践

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**

Made with ❤️ by SEM Team

</div>